namespace ArbitrageBot.Core.Models;

/// <summary>
/// Represents account balance for a specific asset on an exchange
/// </summary>
/// <param name="Exchange">Name of the exchange</param>
/// <param name="Asset">Asset symbol (e.g., "BTC", "USDT")</param>
/// <param name="Free">Available balance for trading</param>
/// <param name="Locked">Balance locked in open orders</param>
public record Balance(
    string Exchange,
    string Asset,
    decimal Free,
    decimal Locked
)
{
    /// <summary>
    /// Total balance (Free + Locked)
    /// </summary>
    public decimal Total => Free + Locked;
};
