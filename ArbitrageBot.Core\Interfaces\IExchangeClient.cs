using ArbitrageBot.Core.Models;

namespace ArbitrageBot.Core.Interfaces;

/// <summary>
/// Interface for interacting with cryptocurrency exchanges
/// </summary>
public interface IExchangeClient
{
    /// <summary>
    /// Name of the exchange this client connects to
    /// </summary>
    string ExchangeName { get; }

    /// <summary>
    /// Places a new order on the exchange
    /// </summary>
    /// <param name="symbol">Trading pair symbol</param>
    /// <param name="orderType">Type of order (Market, Limit, etc.)</param>
    /// <param name="side">Buy or Sell</param>
    /// <param name="amount">Amount to trade</param>
    /// <param name="price">Price for limit orders (optional for market orders)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The created order</returns>
    Task<Order> PlaceOrderAsync(
        string symbol,
        string orderType,
        string side,
        decimal amount,
        decimal? price = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancels an existing order
    /// </summary>
    /// <param name="orderId">Exchange-specific order ID</param>
    /// <param name="symbol">Trading pair symbol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The cancelled order</returns>
    Task<Order> CancelOrderAsync(
        string orderId,
        string symbol,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the status of a specific order
    /// </summary>
    /// <param name="orderId">Exchange-specific order ID</param>
    /// <param name="symbol">Trading pair symbol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The order with current status</returns>
    Task<Order> GetOrderStatusAsync(
        string orderId,
        string symbol,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets account balances for all assets
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of balances</returns>
    Task<IEnumerable<Balance>> GetBalancesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets balance for a specific asset
    /// </summary>
    /// <param name="asset">Asset symbol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Balance for the specified asset</returns>
    Task<Balance?> GetBalanceAsync(string asset, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current ticker price for a symbol
    /// </summary>
    /// <param name="symbol">Trading pair symbol</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current price information</returns>
    Task<decimal> GetTickerPriceAsync(string symbol, CancellationToken cancellationToken = default);

    /// <summary>
    /// Subscribes to real-time price updates for specified symbols
    /// </summary>
    /// <param name="symbols">Trading pair symbols to monitor</param>
    /// <param name="onPriceUpdate">Callback for price updates</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the subscription lifecycle</returns>
    Task SubscribeToPriceUpdatesAsync(
        IEnumerable<string> symbols,
        Func<string, decimal, DateTime, Task> onPriceUpdate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tests connectivity to the exchange
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is successful</returns>
    Task<bool> TestConnectivityAsync(CancellationToken cancellationToken = default);
}
