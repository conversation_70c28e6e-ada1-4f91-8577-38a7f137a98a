{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ArbitrageBot.Infrastructure/1.0.0": {"dependencies": {"ArbitrageBot.Core": "1.0.0"}, "runtime": {"ArbitrageBot.Infrastructure.dll": {}}}, "ArbitrageBot.Core/1.0.0": {"runtime": {"ArbitrageBot.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"ArbitrageBot.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ArbitrageBot.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}