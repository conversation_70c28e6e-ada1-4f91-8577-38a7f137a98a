using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Infrastructure.Clients;
using Microsoft.Extensions.Logging;

namespace ArbitrageBot.Host;

/// <summary>
/// Simple test class to demonstrate FakeExchangeClient functionality
/// </summary>
public static class TestFakeExchangeClient
{
    public static async Task RunTestsAsync()
    {
        // Create a logger factory
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        var logger = loggerFactory.CreateLogger<FakeExchangeClient>();
        
        // Create fake exchange clients
        using var binanceClient = new FakeExchangeClient("Binance", logger);
        using var coinbaseClient = new FakeExchangeClient("Coinbase", logger);
        
        Console.WriteLine("=== FakeExchangeClient Test Demo ===\n");
        
        // Test connectivity
        Console.WriteLine("1. Testing Connectivity:");
        var binanceConnected = await binanceClient.TestConnectivityAsync();
        var coinbaseConnected = await coinbaseClient.TestConnectivityAsync();
        Console.WriteLine($"   Binance Connected: {binanceConnected}");
        Console.WriteLine($"   Coinbase Connected: {coinbaseConnected}\n");
        
        // Test balances
        Console.WriteLine("2. Getting Initial Balances:");
        await DisplayBalances(binanceClient);
        await DisplayBalances(coinbaseClient);
        
        // Test price retrieval
        Console.WriteLine("3. Getting Current Prices:");
        await DisplayPrice(binanceClient, "BTC/USDT");
        await DisplayPrice(coinbaseClient, "BTC/USDT");
        await DisplayPrice(binanceClient, "ETH/USDT");
        await DisplayPrice(coinbaseClient, "ETH/USDT");
        
        // Test order placement
        Console.WriteLine("4. Placing Orders:");
        await PlaceTestOrder(binanceClient, "BTC/USDT", "Market", "Buy", 0.01m);
        await PlaceTestOrder(coinbaseClient, "ETH/USDT", "Limit", "Sell", 0.5m, 3100m);
        
        // Wait a moment for order execution simulation
        Console.WriteLine("\n5. Waiting for order execution simulation...");
        await Task.Delay(2000);
        
        // Check balances after orders
        Console.WriteLine("6. Balances After Orders:");
        await DisplayBalances(binanceClient);
        await DisplayBalances(coinbaseClient);
        
        // Test price subscription
        Console.WriteLine("7. Testing Price Subscriptions (5 seconds):");
        var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        
        var subscriptionTask = binanceClient.SubscribeToPriceUpdatesAsync(
            new[] { "BTC/USDT", "ETH/USDT" },
            async (symbol, price, timestamp) =>
            {
                Console.WriteLine($"   Price Update: {symbol} = ${price:F2} at {timestamp:HH:mm:ss}");
                await Task.CompletedTask;
            },
            cancellationTokenSource.Token);
        
        try
        {
            await subscriptionTask;
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("   Price subscription ended.\n");
        }
        
        Console.WriteLine("=== Test Demo Complete ===");
    }
    
    private static async Task DisplayBalances(IExchangeClient client)
    {
        var balances = await client.GetBalancesAsync();
        Console.WriteLine($"   {client.ExchangeName} Balances:");
        foreach (var balance in balances.Where(b => b.Free > 0))
        {
            Console.WriteLine($"     {balance.Asset}: {balance.Free:F8} (Free), {balance.Locked:F8} (Locked)");
        }
        Console.WriteLine();
    }
    
    private static async Task DisplayPrice(IExchangeClient client, string symbol)
    {
        var price = await client.GetTickerPriceAsync(symbol);
        Console.WriteLine($"   {client.ExchangeName} {symbol}: ${price:F2}");
    }
    
    private static async Task PlaceTestOrder(IExchangeClient client, string symbol, string orderType, string side, decimal amount, decimal? price = null)
    {
        try
        {
            var order = await client.PlaceOrderAsync(symbol, orderType, side, amount, price);
            Console.WriteLine($"   {client.ExchangeName} Order Placed: {order.Id} - {side} {amount} {symbol} at ${order.Price:F2} ({orderType})");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   Error placing order on {client.ExchangeName}: {ex.Message}");
        }
    }
}
