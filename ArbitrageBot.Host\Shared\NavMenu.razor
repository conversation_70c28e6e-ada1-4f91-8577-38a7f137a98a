<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="fas fa-robot"></i>
            ArbitrageBot
        </a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="CollapseNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-home" aria-hidden="true"></span> Dashboard
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="opportunities">
                <span class="oi oi-list-rich" aria-hidden="true"></span> Opportunities
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="prices">
                <span class="oi oi-dollar" aria-hidden="true"></span> Live Prices
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="settings">
                <span class="oi oi-cog" aria-hidden="true"></span> Settings
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void CollapseNavMenu()
    {
        collapseNavMenu = true;
    }
}
