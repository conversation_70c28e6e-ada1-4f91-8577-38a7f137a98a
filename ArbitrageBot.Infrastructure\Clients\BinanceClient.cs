using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Core.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace ArbitrageBot.Infrastructure.Clients;

/// <summary>
/// Binance exchange client implementation
/// </summary>
public class BinanceClient : IExchangeClient
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<BinanceClient> _logger;
    
    private readonly string _apiKey;
    private readonly string _apiSecret;
    private readonly string _baseUrl;
    
    // Binance API endpoints
    private const string ACCOUNT_ENDPOINT = "/api/v3/account";
    private const string ORDER_ENDPOINT = "/api/v3/order";
    private const string TICKER_PRICE_ENDPOINT = "/api/v3/ticker/price";
    private const string PING_ENDPOINT = "/api/v3/ping";
    
    public BinanceClient(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<BinanceClient> logger)
    {
        _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Read API credentials from configuration (Secret Manager)
        _apiKey = _configuration["Binance:ApiKey"] ?? "demo-api-key";
        _apiSecret = _configuration["Binance:ApiSecret"] ?? "demo-api-secret";

        // Log warning if using demo credentials
        if (_apiKey == "demo-api-key" || _apiSecret == "demo-api-secret")
        {
            _logger.LogWarning("Using demo API credentials. Set real credentials using: dotnet user-secrets set \"Binance:ApiKey\" \"your-key\"");
        }
        
        // Use testnet for development, mainnet for production
        var useTestnet = _configuration.GetValue<bool>("Binance:UseTestnet", true);
        _baseUrl = useTestnet 
            ? "https://testnet.binance.vision" 
            : "https://api.binance.com";
        
        _logger.LogInformation("BinanceClient initialized with base URL: {BaseUrl}", _baseUrl);
    }

    public string ExchangeName => "Binance";

    public async Task<Order> PlaceOrderAsync(string symbol, string orderType, string side, decimal amount, decimal? price = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if using demo credentials
            if (_apiKey == "demo-api-key" || _apiSecret == "demo-api-secret")
            {
                _logger.LogWarning("Cannot place real orders with demo credentials. Configure real API keys to place orders.");
                throw new InvalidOperationException("Demo credentials cannot be used for placing orders. Configure real API keys using Secret Manager.");
            }

            _logger.LogInformation("Placing {OrderType} {Side} order: {Amount} {Symbol} at {Price}",
                orderType, side, amount, symbol, price?.ToString() ?? "market");

            var httpClient = _httpClientFactory.CreateClient();
            
            // Convert our generic parameters to Binance-specific format
            var binanceSymbol = ConvertSymbolToBinanceFormat(symbol);
            var binanceSide = side.ToUpper();
            var binanceOrderType = ConvertOrderTypeToBinanceFormat(orderType);
            
            // Build request parameters
            var parameters = new Dictionary<string, string>
            {
                ["symbol"] = binanceSymbol,
                ["side"] = binanceSide,
                ["type"] = binanceOrderType,
                ["quantity"] = amount.ToString("F8", CultureInfo.InvariantCulture),
                ["timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()
            };
            
            // Add price for limit orders
            if (binanceOrderType == "LIMIT" && price.HasValue)
            {
                parameters["price"] = price.Value.ToString("F8", CultureInfo.InvariantCulture);
                parameters["timeInForce"] = "GTC"; // Good Till Cancelled
            }
            
            // Create signature
            var queryString = CreateQueryString(parameters);
            var signature = CreateSignature(queryString);
            parameters["signature"] = signature;
            
            // Create the request
            var requestUrl = $"{_baseUrl}{ORDER_ENDPOINT}";
            var content = new FormUrlEncodedContent(parameters);
            
            httpClient.DefaultRequestHeaders.Add("X-MBX-APIKEY", _apiKey);
            
            var response = await httpClient.PostAsync(requestUrl, content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Binance API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new HttpRequestException($"Binance API error: {response.StatusCode} - {responseContent}");
            }
            
            // Parse response
            var binanceOrder = JsonSerializer.Deserialize<BinanceOrderResponse>(responseContent);
            if (binanceOrder == null)
            {
                throw new InvalidOperationException("Failed to parse Binance order response");
            }
            
            var order = new Order(
                Id: Guid.NewGuid(), // We'll use our own GUID, store Binance order ID separately if needed
                Exchange: ExchangeName,
                Symbol: symbol, // Convert back to our format
                OrderType: orderType,
                Side: side,
                Price: decimal.Parse(binanceOrder.Price, CultureInfo.InvariantCulture),
                Amount: decimal.Parse(binanceOrder.OrigQty, CultureInfo.InvariantCulture),
                Status: ConvertBinanceStatusToOurFormat(binanceOrder.Status)
            );
            
            _logger.LogInformation("Order placed successfully: {OrderId} (Binance: {BinanceOrderId})", 
                order.Id, binanceOrder.OrderId);
            
            return order;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error placing order on Binance: {Symbol} {Side} {Amount}", symbol, side, amount);
            throw;
        }
    }

    public async Task<IEnumerable<Balance>> GetBalancesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if using demo credentials
            if (_apiKey == "demo-api-key" || _apiSecret == "demo-api-secret")
            {
                _logger.LogWarning("Cannot fetch real balances with demo credentials. Configure real API keys to get balances.");
                throw new InvalidOperationException("Demo credentials cannot be used for fetching balances. Configure real API keys using Secret Manager.");
            }

            _logger.LogDebug("Fetching account balances from Binance");

            var httpClient = _httpClientFactory.CreateClient();
            
            // Build request parameters
            var parameters = new Dictionary<string, string>
            {
                ["timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()
            };
            
            // Create signature
            var queryString = CreateQueryString(parameters);
            var signature = CreateSignature(queryString);
            
            // Create the request
            var requestUrl = $"{_baseUrl}{ACCOUNT_ENDPOINT}?{queryString}&signature={signature}";
            
            httpClient.DefaultRequestHeaders.Add("X-MBX-APIKEY", _apiKey);
            
            var response = await httpClient.GetAsync(requestUrl, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Binance API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new HttpRequestException($"Binance API error: {response.StatusCode} - {responseContent}");
            }
            
            // Parse response
            var accountInfo = JsonSerializer.Deserialize<BinanceAccountResponse>(responseContent);
            if (accountInfo?.Balances == null)
            {
                throw new InvalidOperationException("Failed to parse Binance account response");
            }
            
            var balances = accountInfo.Balances
                .Where(b => decimal.Parse(b.Free, CultureInfo.InvariantCulture) > 0 || 
                           decimal.Parse(b.Locked, CultureInfo.InvariantCulture) > 0)
                .Select(b => new Balance(
                    Exchange: ExchangeName,
                    Asset: b.Asset,
                    Free: decimal.Parse(b.Free, CultureInfo.InvariantCulture),
                    Locked: decimal.Parse(b.Locked, CultureInfo.InvariantCulture)
                ))
                .ToList();
            
            _logger.LogInformation("Retrieved {Count} balances from Binance", balances.Count);
            return balances;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching balances from Binance");
            throw;
        }
    }

    public async Task<Balance?> GetBalanceAsync(string asset, CancellationToken cancellationToken = default)
    {
        var balances = await GetBalancesAsync(cancellationToken);
        return balances.FirstOrDefault(b => b.Asset.Equals(asset, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<decimal> GetTickerPriceAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Fetching ticker price for {Symbol} from Binance", symbol);

            var httpClient = _httpClientFactory.CreateClient();
            var binanceSymbol = ConvertSymbolToBinanceFormat(symbol);

            var requestUrl = $"{_baseUrl}{TICKER_PRICE_ENDPOINT}?symbol={binanceSymbol}";

            var response = await httpClient.GetAsync(requestUrl, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Binance API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                throw new HttpRequestException($"Binance API error: {response.StatusCode} - {responseContent}");
            }

            var tickerPrice = JsonSerializer.Deserialize<BinanceTickerPriceResponse>(responseContent);
            if (tickerPrice == null)
            {
                throw new InvalidOperationException("Failed to parse Binance ticker price response");
            }

            var price = decimal.Parse(tickerPrice.Price, CultureInfo.InvariantCulture);
            _logger.LogDebug("Retrieved price for {Symbol}: {Price}", symbol, price);

            return price;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching ticker price from Binance: {Symbol}", symbol);
            throw;
        }
    }

    public async Task<bool> TestConnectivityAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Testing connectivity to Binance");

            var httpClient = _httpClientFactory.CreateClient();
            var requestUrl = $"{_baseUrl}{PING_ENDPOINT}";

            var response = await httpClient.GetAsync(requestUrl, cancellationToken);
            var isConnected = response.IsSuccessStatusCode;

            _logger.LogInformation("Binance connectivity test: {Result}", isConnected ? "Success" : "Failed");
            return isConnected;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing connectivity to Binance");
            return false;
        }
    }

    // Placeholder implementations for methods not yet implemented
    public Task<Order> CancelOrderAsync(string orderId, string symbol, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("CancelOrderAsync will be implemented in future iterations");
    }

    public Task<Order> GetOrderStatusAsync(string orderId, string symbol, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("GetOrderStatusAsync will be implemented in future iterations");
    }

    public Task SubscribeToPriceUpdatesAsync(IEnumerable<string> symbols, Func<string, decimal, DateTime, Task> onPriceUpdate, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("SubscribeToPriceUpdatesAsync will be implemented with WebSocket support in future iterations");
    }

    // Helper methods
    private string ConvertSymbolToBinanceFormat(string symbol)
    {
        // Convert "BTC/USDT" to "BTCUSDT"
        return symbol.Replace("/", "").ToUpper();
    }

    private string ConvertOrderTypeToBinanceFormat(string orderType)
    {
        return orderType.ToUpper() switch
        {
            "MARKET" => "MARKET",
            "LIMIT" => "LIMIT",
            _ => throw new ArgumentException($"Unsupported order type: {orderType}")
        };
    }

    private string ConvertBinanceStatusToOurFormat(string binanceStatus)
    {
        return binanceStatus switch
        {
            "NEW" => "NEW",
            "PARTIALLY_FILLED" => "PARTIALLY_FILLED",
            "FILLED" => "FILLED",
            "CANCELED" => "CANCELLED",
            "REJECTED" => "REJECTED",
            "EXPIRED" => "EXPIRED",
            _ => binanceStatus
        };
    }

    private string CreateQueryString(Dictionary<string, string> parameters)
    {
        return string.Join("&", parameters.Select(kvp => $"{kvp.Key}={kvp.Value}"));
    }

    private string CreateSignature(string queryString)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_apiSecret));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(queryString));
        return Convert.ToHexString(hash).ToLower();
    }
}

// Response models for Binance API
internal record BinanceOrderResponse(
    long OrderId,
    string Symbol,
    string Status,
    string Type,
    string Side,
    string OrigQty,
    string Price,
    string ExecutedQty,
    string CummulativeQuoteQty,
    long TransactTime
);

internal record BinanceAccountResponse(
    BinanceBalanceResponse[] Balances
);

internal record BinanceBalanceResponse(
    string Asset,
    string Free,
    string Locked
);

internal record BinanceTickerPriceResponse(
    string Symbol,
    string Price
);
