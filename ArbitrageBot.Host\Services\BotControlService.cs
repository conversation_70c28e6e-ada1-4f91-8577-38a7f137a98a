using System.Collections.Concurrent;

namespace ArbitrageBot.Host.Services;

/// <summary>
/// Thread-safe service for controlling the arbitrage bot's worker services
/// </summary>
public class BotControlService : IBotControlService, IDisposable
{
    private readonly ILogger<BotControlService> _logger;
    private readonly object _statusLock = new();
    
    private BotStatus _status = BotStatus.Stopped;
    private CancellationTokenSource? _botCancellationTokenSource;
    private DateTime? _startedAt;
    private DateTime? _stoppedAt;
    private string? _lastError;
    private bool _disposed;

    public BotControlService(ILogger<BotControlService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public BotStatus Status
    {
        get
        {
            lock (_statusLock)
            {
                return _status;
            }
        }
    }

    public CancellationToken BotCancellationToken
    {
        get
        {
            lock (_statusLock)
            {
                return _botCancellationTokenSource?.Token ?? CancellationToken.None;
            }
        }
    }

    public event EventHandler<BotStatusChangedEventArgs>? StatusChanged;

    public async Task<bool> StartBotAsync()
    {
        lock (_statusLock)
        {
            if (_status == BotStatus.Running || _status == BotStatus.Starting)
            {
                _logger.LogWarning("Bot is already running or starting. Current status: {Status}", _status);
                return false;
            }

            _logger.LogInformation("Starting arbitrage bot...");
            
            // Create new cancellation token source
            _botCancellationTokenSource?.Dispose();
            _botCancellationTokenSource = new CancellationTokenSource();
            
            // Update status
            var previousStatus = _status;
            _status = BotStatus.Starting;
            _startedAt = DateTime.UtcNow;
            _stoppedAt = null;
            _lastError = null;
            
            // Raise status changed event
            OnStatusChanged(previousStatus, _status);
        }

        try
        {
            // Simulate startup delay (in real implementation, this might involve
            // initializing connections, validating configurations, etc.)
            await Task.Delay(1000, CancellationToken.None);
            
            lock (_statusLock)
            {
                if (_botCancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    _logger.LogWarning("Bot startup was cancelled");
                    return false;
                }
                
                var previousStatus = _status;
                _status = BotStatus.Running;
                OnStatusChanged(previousStatus, _status);
            }
            
            _logger.LogInformation("Arbitrage bot started successfully at {StartTime}", _startedAt);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start arbitrage bot");
            
            lock (_statusLock)
            {
                var previousStatus = _status;
                _status = BotStatus.Stopped;
                _lastError = ex.Message;
                _stoppedAt = DateTime.UtcNow;
                OnStatusChanged(previousStatus, _status);
            }
            
            return false;
        }
    }

    public async Task<bool> StopBotAsync()
    {
        lock (_statusLock)
        {
            if (_status == BotStatus.Stopped || _status == BotStatus.Stopping)
            {
                _logger.LogWarning("Bot is already stopped or stopping. Current status: {Status}", _status);
                return false;
            }

            _logger.LogInformation("Stopping arbitrage bot...");
            
            var previousStatus = _status;
            _status = BotStatus.Stopping;
            OnStatusChanged(previousStatus, _status);
            
            // Cancel the bot operations
            _botCancellationTokenSource?.Cancel();
        }

        try
        {
            // Simulate shutdown delay (in real implementation, this might involve
            // gracefully shutting down connections, saving state, etc.)
            await Task.Delay(500, CancellationToken.None);
            
            lock (_statusLock)
            {
                var previousStatus = _status;
                _status = BotStatus.Stopped;
                _stoppedAt = DateTime.UtcNow;
                OnStatusChanged(previousStatus, _status);
            }
            
            _logger.LogInformation("Arbitrage bot stopped successfully at {StopTime}", _stoppedAt);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping arbitrage bot");
            
            lock (_statusLock)
            {
                var previousStatus = _status;
                _status = BotStatus.Stopped;
                _lastError = ex.Message;
                _stoppedAt = DateTime.UtcNow;
                OnStatusChanged(previousStatus, _status);
            }
            
            return false;
        }
    }

    public BotStatusInfo GetStatusInfo()
    {
        lock (_statusLock)
        {
            TimeSpan? uptime = null;
            if (_status == BotStatus.Running && _startedAt.HasValue)
            {
                uptime = DateTime.UtcNow - _startedAt.Value;
            }
            
            return new BotStatusInfo(
                Status: _status,
                StartedAt: _startedAt,
                StoppedAt: _stoppedAt,
                Uptime: uptime,
                LastError: _lastError
            );
        }
    }

    private void OnStatusChanged(BotStatus previousStatus, BotStatus newStatus)
    {
        try
        {
            StatusChanged?.Invoke(this, new BotStatusChangedEventArgs(previousStatus, newStatus));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while raising StatusChanged event");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _botCancellationTokenSource?.Dispose();
            _disposed = true;
        }
    }
}
