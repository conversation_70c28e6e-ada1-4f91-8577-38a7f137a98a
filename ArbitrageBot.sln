﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArbitrageBot.Core", "ArbitrageBot.Core\ArbitrageBot.Core.csproj", "{A3EEED16-CAA3-479E-B921-4193004BAEF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArbitrageBot.Infrastructure", "ArbitrageBot.Infrastructure\ArbitrageBot.Infrastructure.csproj", "{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArbitrageBot.Host", "ArbitrageBot.Host\ArbitrageBot.Host.csproj", "{A86B038A-D697-4962-B74F-67D4D8E215C5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|x64.Build.0 = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Debug|x86.Build.0 = Debug|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|x64.ActiveCfg = Release|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|x64.Build.0 = Release|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|x86.ActiveCfg = Release|Any CPU
		{A3EEED16-CAA3-479E-B921-4193004BAEF8}.Release|x86.Build.0 = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|x64.Build.0 = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Debug|x86.Build.0 = Debug|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|x64.ActiveCfg = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|x64.Build.0 = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|x86.ActiveCfg = Release|Any CPU
		{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}.Release|x86.Build.0 = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|x64.Build.0 = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Debug|x86.Build.0 = Debug|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|x64.ActiveCfg = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|x64.Build.0 = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|x86.ActiveCfg = Release|Any CPU
		{A86B038A-D697-4962-B74F-67D4D8E215C5}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
