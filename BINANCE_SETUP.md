# Binance Client Setup Guide

This guide explains how to configure and use the BinanceClient for real cryptocurrency trading operations.

## 🔐 API Key Management with .NET Secret Manager

### 1. Initialize Secret Manager

The project is already configured with Secret Manager. If you need to reinitialize:

```bash
dotnet user-secrets init --project ArbitrageBot.Host
```

### 2. Set Your Binance API Credentials

**⚠️ IMPORTANT: Never commit real API keys to source control!**

```bash
# Set your actual Binance API key
dotnet user-secrets set "Binance:ApiKey" "your-actual-binance-api-key" --project ArbitrageBot.Host

# Set your actual Binance API secret
dotnet user-secrets set "Binance:ApiSecret" "your-actual-binance-api-secret" --project ArbitrageBot.Host

# Set to true for testnet, false for mainnet (BE CAREFUL!)
dotnet user-secrets set "Binance:UseTestnet" "true" --project ArbitrageBot.Host
```

### 3. View Current Secrets

```bash
cd ArbitrageBot.Host
dotnet user-secrets list
```

### 4. Remove Secrets (if needed)

```bash
dotnet user-secrets remove "Binance:ApiKey" --project ArbitrageBot.Host
dotnet user-secrets remove "Binance:ApiSecret" --project ArbitrageBot.Host
```

## 🏗️ Getting Binance API Keys

### For Testing (Testnet)
1. Go to [Binance Testnet](https://testnet.binance.vision/)
2. Create an account or log in
3. Generate API keys in the API Management section
4. Set `UseTestnet: true` in your secrets

### For Production (Mainnet)
1. Go to [Binance](https://www.binance.com/)
2. Create an account and complete verification
3. Go to API Management in your account settings
4. Create a new API key with appropriate permissions
5. Set `UseTestnet: false` in your secrets

**⚠️ SECURITY RECOMMENDATIONS:**
- Enable IP restrictions for your API keys
- Only grant necessary permissions (Spot Trading, Read Info)
- Never share your API secret
- Use testnet for development and testing

## 🔧 BinanceClient Configuration

The BinanceClient is configured in `Program.cs`:

```csharp
// Register real Binance client
builder.Services.AddKeyedSingleton<IExchangeClient>("BinanceReal", (provider, key) =>
{
    var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
    var configuration = provider.GetRequiredService<IConfiguration>();
    var logger = provider.GetRequiredService<ILogger<BinanceClient>>();
    return new BinanceClient(httpClientFactory, configuration, logger);
});
```

## 🚀 Using the BinanceClient

### 1. Dependency Injection

```csharp
// In a controller or service
public class TradingService
{
    private readonly IExchangeClient _binanceClient;
    
    public TradingService([FromKeyedServices("BinanceReal")] IExchangeClient binanceClient)
    {
        _binanceClient = binanceClient;
    }
}
```

### 2. Available Methods

#### Test Connectivity
```csharp
var isConnected = await _binanceClient.TestConnectivityAsync();
```

#### Get Ticker Price
```csharp
var price = await _binanceClient.GetTickerPriceAsync("BTC/USDT");
```

#### Get Account Balances
```csharp
var balances = await _binanceClient.GetBalancesAsync();
foreach (var balance in balances)
{
    Console.WriteLine($"{balance.Asset}: {balance.Free} (Free), {balance.Locked} (Locked)");
}
```

#### Place Orders
```csharp
// Market Buy Order
var marketOrder = await _binanceClient.PlaceOrderAsync(
    symbol: "BTC/USDT",
    orderType: "Market",
    side: "Buy",
    amount: 0.001m
);

// Limit Sell Order
var limitOrder = await _binanceClient.PlaceOrderAsync(
    symbol: "ETH/USDT",
    orderType: "Limit",
    side: "Sell",
    amount: 0.1m,
    price: 3500m
);
```

## 🧪 Testing the Implementation

### 1. Build the Project
```bash
dotnet build ArbitrageBot.sln
```

### 2. Run the Application
```bash
dotnet run --project ArbitrageBot.Host
```

### 3. Test API Endpoints

#### Test Connectivity
```bash
curl http://localhost:5079/api/binance/connectivity
```

#### Get Price
```bash
curl http://localhost:5079/api/binance/price/BTCUSDT
```

## 🔍 Implemented Features

### ✅ Currently Implemented
- **TestConnectivityAsync** - Tests connection to Binance API
- **GetTickerPriceAsync** - Retrieves current market prices
- **GetBalancesAsync** - Fetches account balances
- **GetBalanceAsync** - Gets balance for specific asset
- **PlaceOrderAsync** - Places market and limit orders

### 🚧 Not Yet Implemented (Future)
- **CancelOrderAsync** - Cancel existing orders
- **GetOrderStatusAsync** - Check order status
- **SubscribeToPriceUpdatesAsync** - WebSocket price subscriptions

## 🛡️ Security Features

### API Authentication
- Uses HMAC-SHA256 signature for request authentication
- Includes timestamp to prevent replay attacks
- API keys stored securely using .NET Secret Manager

### Error Handling
- Comprehensive logging for all API operations
- Proper exception handling with detailed error messages
- HTTP status code validation

### Configuration
- Testnet/Mainnet switching via configuration
- Secure credential management
- No hardcoded API keys or secrets

## 🔄 Symbol Format Conversion

The BinanceClient automatically converts between formats:
- **Your format**: `"BTC/USDT"`, `"ETH/USDT"`
- **Binance format**: `"BTCUSDT"`, `"ETHUSDT"`

## 📊 Response Models

The client uses internal response models that map Binance API responses to our domain models:

```csharp
// Our domain model
public record Order(
    Guid Id,
    string Exchange,
    string Symbol,
    string OrderType,
    string Side,
    decimal Price,
    decimal Amount,
    string Status
);

// Binance API response (internal)
internal record BinanceOrderResponse(
    long OrderId,
    string Symbol,
    string Status,
    string Type,
    string Side,
    string OrigQty,
    string Price,
    string ExecutedQty,
    string CummulativeQuoteQty,
    long TransactTime
);
```

## 🚨 Important Notes

1. **Always test with testnet first** before using real money
2. **Monitor API rate limits** to avoid being banned
3. **Keep your API keys secure** and rotate them regularly
4. **Use appropriate order sizes** for testing
5. **Implement proper error handling** in production code

## 📞 Support

If you encounter issues:
1. Check the application logs for detailed error messages
2. Verify your API keys are correctly configured
3. Ensure you have sufficient permissions on your Binance API key
4. Check Binance API status and rate limits
