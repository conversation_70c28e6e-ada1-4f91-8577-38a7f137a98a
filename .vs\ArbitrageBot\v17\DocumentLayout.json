{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.host\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|solutionrelative:arbitragebot.host\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.host\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|solutionrelative:arbitragebot.host\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.host\\controllers\\botcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|solutionrelative:arbitragebot.host\\controllers\\botcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.core\\models\\order.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|solutionrelative:arbitragebot.core\\models\\order.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.infrastructure\\clients\\binanceclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|solutionrelative:arbitragebot.infrastructure\\clients\\binanceclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.host\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A86B038A-D697-4962-B74F-67D4D8E215C5}|ArbitrageBot.Host\\ArbitrageBot.Host.csproj|solutionrelative:arbitragebot.host\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.core\\models\\balance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|solutionrelative:arbitragebot.core\\models\\balance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.core\\models\\arbitrageopportunity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A3EEED16-CAA3-479E-B921-4193004BAEF8}|ArbitrageBot.Core\\ArbitrageBot.Core.csproj|solutionrelative:arbitragebot.core\\models\\arbitrageopportunity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.infrastructure\\clients\\fakeexchangeclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|solutionrelative:arbitragebot.infrastructure\\clients\\fakeexchangeclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\crypt\\arbitragebot.infrastructure\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{24E4B76E-AA43-4004-BC76-16EDA9AFA2C6}|ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj|solutionrelative:arbitragebot.infrastructure\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeToolTip": "..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAANMTAAAAAAAAAAASwOATAABRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-06-30T12:48:04.06Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\Program.cs", "RelativeDocumentMoniker": "ArbitrageBot.Host\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\Program.cs", "RelativeToolTip": "ArbitrageBot.Host\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T12:47:21.953Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\appsettings.Development.json", "RelativeDocumentMoniker": "ArbitrageBot.Host\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\appsettings.Development.json", "RelativeToolTip": "ArbitrageBot.Host\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-30T11:59:00.911Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\appsettings.json", "RelativeDocumentMoniker": "ArbitrageBot.Host\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\appsettings.json", "RelativeToolTip": "ArbitrageBot.Host\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-30T11:58:57.573Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "BinanceClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Clients\\BinanceClient.cs", "RelativeDocumentMoniker": "ArbitrageBot.Infrastructure\\Clients\\BinanceClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Clients\\BinanceClient.cs", "RelativeToolTip": "ArbitrageBot.Infrastructure\\Clients\\BinanceClient.cs", "ViewState": "AgIAAHcAAAAAAAAAAAAwwH0AAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:58:42.214Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "BotController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\Controllers\\BotController.cs", "RelativeDocumentMoniker": "ArbitrageBot.Host\\Controllers\\BotController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\Controllers\\BotController.cs", "RelativeToolTip": "ArbitrageBot.Host\\Controllers\\BotController.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:58:34.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Order.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\Order.cs", "RelativeDocumentMoniker": "ArbitrageBot.Core\\Models\\Order.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\Order.cs", "RelativeToolTip": "ArbitrageBot.Core\\Models\\Order.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:58:31.648Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Balance.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\Balance.cs", "RelativeDocumentMoniker": "ArbitrageBot.Core\\Models\\Balance.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\Balance.cs", "RelativeToolTip": "ArbitrageBot.Core\\Models\\Balance.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:58:31.048Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ArbitrageOpportunity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\ArbitrageOpportunity.cs", "RelativeDocumentMoniker": "ArbitrageBot.Core\\Models\\ArbitrageOpportunity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\Models\\ArbitrageOpportunity.cs", "RelativeToolTip": "ArbitrageBot.Core\\Models\\ArbitrageOpportunity.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:58:26.926Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "FakeExchangeClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Clients\\FakeExchangeClient.cs", "RelativeDocumentMoniker": "ArbitrageBot.Infrastructure\\Clients\\FakeExchangeClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Clients\\FakeExchangeClient.cs", "RelativeToolTip": "ArbitrageBot.Infrastructure\\Clients\\FakeExchangeClient.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T09:33:42.498Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AppDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "ArbitrageBot.Infrastructure\\Data\\AppDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\Data\\AppDbContext.cs", "RelativeToolTip": "ArbitrageBot.Infrastructure\\Data\\AppDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T09:33:40.613Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{56df62a4-05a3-4e5b-aa1a-99371ccfb997}"}]}]}]}