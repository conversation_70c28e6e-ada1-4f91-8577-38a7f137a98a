﻿// <auto-generated />
using System;
using ArbitrageBot.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace ArbitrageBot.Infrastructure.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250630091840_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("ArbitrageBot.Core.Models.ArbitrageOpportunity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DetectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("HighPrice")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("HighPriceExchange")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("LowPrice")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("LowPriceExchange")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("PotentialProfitPercentage")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<string>("TargetSymbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("DetectedAt");

                    b.HasIndex("PotentialProfitPercentage");

                    b.HasIndex("TargetSymbol");

                    b.HasIndex("LowPriceExchange", "HighPriceExchange");

                    b.ToTable("ArbitrageOpportunities");
                });

            modelBuilder.Entity("ArbitrageBot.Core.Models.Balance", b =>
                {
                    b.Property<string>("Exchange")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Asset")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("Free")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<decimal>("Locked")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.HasKey("Exchange", "Asset");

                    b.HasIndex("Asset");

                    b.HasIndex("Exchange");

                    b.ToTable("Balances");
                });

            modelBuilder.Entity("ArbitrageBot.Core.Models.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("Exchange")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OrderType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("Side")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("Exchange");

                    b.HasIndex("Status");

                    b.HasIndex("Symbol");

                    b.HasIndex("Exchange", "Symbol");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("ArbitrageBot.Core.Models.Trade", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("Exchange")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Exchange");

                    b.HasIndex("Symbol");

                    b.HasIndex("Timestamp");

                    b.HasIndex("Exchange", "Symbol");

                    b.ToTable("Trades");
                });
#pragma warning restore 612, 618
        }
    }
}
