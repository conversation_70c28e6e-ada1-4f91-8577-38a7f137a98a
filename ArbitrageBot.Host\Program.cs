using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Host;
using ArbitrageBot.Host.Workers;
using ArbitrageBot.Infrastructure.Clients;
using ArbitrageBot.Infrastructure.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Configure Entity Framework with PostgreSQL (commented out for demo)
// builder.Services.AddDbContext<AppDbContext>(options =>
//     options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Configure HttpClient for external API calls
builder.Services.AddHttpClient();

// Register exchange clients
builder.Services.AddSingleton<IExchangeClient>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<FakeExchangeClient>>();
    return new FakeExchangeClient("FakeExchange", logger);
});

// You can register multiple fake exchanges for testing arbitrage
builder.Services.AddKeyedSingleton<IExchangeClient>("Binance", (provider, key) =>
{
    var logger = provider.GetRequiredService<ILogger<FakeExchangeClient>>();
    return new FakeExchangeClient("Binance", logger);
});

builder.Services.AddKeyedSingleton<IExchangeClient>("Coinbase", (provider, key) =>
{
    var logger = provider.GetRequiredService<ILogger<FakeExchangeClient>>();
    return new FakeExchangeClient("Coinbase", logger);
});

// Register real Binance client
builder.Services.AddKeyedSingleton<IExchangeClient>("BinanceReal", (provider, key) =>
{
    var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
    var configuration = provider.GetRequiredService<IConfiguration>();
    var logger = provider.GetRequiredService<ILogger<BinanceClient>>();
    return new BinanceClient(httpClientFactory, configuration, logger);
});

// Register hosted services (background workers)
builder.Services.AddHostedService<MarketDataCollectorWorker>();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Check if we should run the FakeExchangeClient test
if (args.Length > 0 && args[0] == "--test-fake-client")
{
    await TestFakeExchangeClient.RunTestsAsync();
    return;
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Health check endpoint
app.MapGet("/health", () =>
{
    return Results.Ok(new {
        Status = "Healthy",
        Database = "Skipped for demo",
        Timestamp = DateTime.UtcNow
    });
})
.WithName("HealthCheck")
.WithOpenApi();

// Test endpoints for FakeExchangeClient
app.MapGet("/api/exchange/balances", async (IExchangeClient exchangeClient) =>
{
    var balances = await exchangeClient.GetBalancesAsync();
    return Results.Ok(new {
        Exchange = exchangeClient.ExchangeName,
        Balances = balances
    });
})
.WithName("GetBalances")
.WithOpenApi();

app.MapGet("/api/exchange/price/{symbol}", async (string symbol, IExchangeClient exchangeClient) =>
{
    try
    {
        var price = await exchangeClient.GetTickerPriceAsync(symbol);
        return Results.Ok(new {
            Exchange = exchangeClient.ExchangeName,
            Symbol = symbol,
            Price = price,
            Timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error getting price for {symbol}: {ex.Message}");
    }
})
.WithName("GetPrice")
.WithOpenApi();

app.MapPost("/api/exchange/order", async (PlaceOrderRequest request, IExchangeClient exchangeClient) =>
{
    try
    {
        var order = await exchangeClient.PlaceOrderAsync(
            request.Symbol,
            request.OrderType,
            request.Side,
            request.Amount,
            request.Price);

        return Results.Ok(order);
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error placing order: {ex.Message}");
    }
})
.WithName("PlaceOrder")
.WithOpenApi();

app.MapGet("/api/exchange/connectivity", async (IExchangeClient exchangeClient) =>
{
    var isConnected = await exchangeClient.TestConnectivityAsync();
    return Results.Ok(new {
        Exchange = exchangeClient.ExchangeName,
        Connected = isConnected,
        Timestamp = DateTime.UtcNow
    });
})
.WithName("TestConnectivity")
.WithOpenApi();

// Test endpoint for real Binance client
app.MapGet("/api/binance/connectivity", async ([FromKeyedServices("BinanceReal")] IExchangeClient binanceClient) =>
{
    try
    {
        var isConnected = await binanceClient.TestConnectivityAsync();
        return Results.Ok(new {
            Exchange = binanceClient.ExchangeName,
            Connected = isConnected,
            Timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error testing Binance connectivity: {ex.Message}");
    }
})
.WithName("TestBinanceConnectivity")
.WithOpenApi();

app.MapGet("/api/binance/price/{symbol}", async (string symbol, [FromKeyedServices("BinanceReal")] IExchangeClient binanceClient) =>
{
    try
    {
        var price = await binanceClient.GetTickerPriceAsync(symbol);
        return Results.Ok(new {
            Exchange = binanceClient.ExchangeName,
            Symbol = symbol,
            Price = price,
            Timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Error getting price from Binance: {ex.Message}");
    }
})
.WithName("GetBinancePrice")
.WithOpenApi();

// API endpoints will be added here later

app.Run();

// Request/Response models for API endpoints
public record PlaceOrderRequest(
    string Symbol,
    string OrderType,
    string Side,
    decimal Amount,
    decimal? Price = null
);
