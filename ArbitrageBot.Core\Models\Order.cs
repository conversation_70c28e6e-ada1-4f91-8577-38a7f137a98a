namespace ArbitrageBot.Core.Models;

/// <summary>
/// Represents an order placed on an exchange
/// </summary>
/// <param name="Id">Unique identifier for the order</param>
/// <param name="Exchange">Name of the exchange where the order is placed</param>
/// <param name="Symbol">Trading pair symbol (e.g., "BTC/USDT")</param>
/// <param name="OrderType">Type of order (e.g., "Limit", "Market")</param>
/// <param name="Side">Order side ("Buy" or "Sell")</param>
/// <param name="Price">Order price (for limit orders)</param>
/// <param name="Amount">Amount of the base asset to trade</param>
/// <param name="Status">Current status of the order</param>
public record Order(
    Guid Id,
    string Exchange,
    string Symbol,
    string OrderType,
    string Side,
    decimal Price,
    decimal Amount,
    string Status
);
