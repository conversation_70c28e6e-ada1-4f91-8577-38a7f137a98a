using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Host.Services;
using ArbitrageBot.Infrastructure.Clients;
using Microsoft.Extensions.Logging;

namespace ArbitrageBot.Host;

/// <summary>
/// Simple test program to verify the arbitrage system functionality
/// </summary>
public static class TestArbitrageSystem
{
    public static async Task RunTest()
    {
        Console.WriteLine("=== Arbitrage System Test ===");
        
        // Create logger factory
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Create price data service
        var priceDataLogger = loggerFactory.CreateLogger<PriceDataService>();
        var priceDataService = new PriceDataService(priceDataLogger);
        
        // Create fake exchange clients
        var binanceLogger = loggerFactory.CreateLogger<FakeExchangeClient>();
        var coinbaseLogger = loggerFactory.CreateLogger<FakeExchangeClient>();
        
        var binanceClient = new FakeExchangeClient("Binance", binanceLogger);
        var coinbaseClient = new FakeExchangeClient("Coinbase", coinbaseLogger);
        
        Console.WriteLine("1. Created exchange clients and price data service");
        
        // Test price collection
        Console.WriteLine("2. Testing price collection...");
        var symbols = new[] { "BTC/USDT", "ETH/USDT", "BNB/USDT" };
        
        foreach (var symbol in symbols)
        {
            var binancePrice = await binanceClient.GetTickerPriceAsync(symbol);
            var coinbasePrice = await coinbaseClient.GetTickerPriceAsync(symbol);
            
            priceDataService.UpdatePrice("Binance", symbol, binancePrice);
            priceDataService.UpdatePrice("Coinbase", symbol, coinbasePrice);
            
            Console.WriteLine($"   {symbol}: Binance=${binancePrice:F2}, Coinbase=${coinbasePrice:F2}");
        }
        
        // Test arbitrage detection
        Console.WriteLine("3. Testing arbitrage detection...");
        foreach (var symbol in symbols)
        {
            var prices = priceDataService.GetPricesForSymbol(symbol);
            if (prices.Count >= 2)
            {
                var exchanges = prices.Keys.ToArray();
                var price1 = prices[exchanges[0]];
                var price2 = prices[exchanges[1]];
                
                var priceDiff = Math.Abs(price1 - price2);
                var profitPercentage = (priceDiff / Math.Min(price1, price2)) * 100;
                
                if (profitPercentage > 0.5m) // 0.5% threshold
                {
                    var buyExchange = price1 < price2 ? exchanges[0] : exchanges[1];
                    var sellExchange = price1 < price2 ? exchanges[1] : exchanges[0];
                    var buyPrice = Math.Min(price1, price2);
                    var sellPrice = Math.Max(price1, price2);
                    
                    Console.WriteLine($"   🚀 OPPORTUNITY: {symbol} - Buy on {buyExchange} at ${buyPrice:F2}, " +
                                    $"Sell on {sellExchange} at ${sellPrice:F2}, Profit: {profitPercentage:F2}%");
                }
                else
                {
                    Console.WriteLine($"   ❌ No opportunity for {symbol} (profit: {profitPercentage:F2}%)");
                }
            }
        }
        
        // Test API data structure
        Console.WriteLine("4. Testing API data structure...");
        var availableSymbols = priceDataService.GetAvailableSymbols();
        var availableExchanges = priceDataService.GetAvailableExchanges();
        
        Console.WriteLine($"   Available symbols: {string.Join(", ", availableSymbols)}");
        Console.WriteLine($"   Available exchanges: {string.Join(", ", availableExchanges)}");
        
        foreach (var symbol in availableSymbols)
        {
            Console.WriteLine($"   {symbol} prices:");
            foreach (var exchange in availableExchanges)
            {
                var price = priceDataService.GetPrice(exchange, symbol);
                var lastUpdate = priceDataService.GetLastUpdateTime(exchange, symbol);
                if (price.HasValue)
                {
                    Console.WriteLine($"     {exchange}: ${price.Value:F8} (updated: {lastUpdate:HH:mm:ss})");
                }
            }
        }
        
        Console.WriteLine("=== Test Complete ===");
        
        // Cleanup
        binanceClient.Dispose();
        coinbaseClient.Dispose();
    }
}
