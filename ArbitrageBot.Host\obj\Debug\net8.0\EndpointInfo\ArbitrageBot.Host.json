{"openapi": "3.0.1", "info": {"title": "ArbitrageBot.Host", "version": "1.0"}, "paths": {"/health": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "HealthCheck", "responses": {"200": {"description": "OK"}}}}, "/api/exchange/balances": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "GetBalances", "responses": {"200": {"description": "OK"}}}}, "/api/exchange/price/{symbol}": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "GetPrice", "parameters": [{"name": "symbol", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/exchange/order": {"post": {"tags": ["ArbitrageBot.Host"], "operationId": "PlaceOrder", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlaceOrderRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/exchange/connectivity": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "TestConnectivity", "responses": {"200": {"description": "OK"}}}}, "/api/binance/connectivity": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "TestBinanceConnectivity", "responses": {"200": {"description": "OK"}}}}, "/api/binance/price/{symbol}": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "GetBinancePrice", "parameters": [{"name": "symbol", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/prices": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "GetAllPrices", "responses": {"200": {"description": "OK"}}}}, "/api/prices/{symbol}": {"get": {"tags": ["ArbitrageBot.Host"], "operationId": "GetPricesForSymbol", "parameters": [{"name": "symbol", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Bot/start": {"post": {"tags": ["Bot"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}}}}, "/api/Bot/stop": {"post": {"tags": ["Bot"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotOperationResult"}}}}}}}, "/api/Bot/status": {"get": {"tags": ["Bot"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BotStatusResponse"}}}}}}}}, "components": {"schemas": {"BotOperationResult": {"required": ["message", "status"], "type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "statusInfo": {"$ref": "#/components/schemas/BotStatusInfo"}}, "additionalProperties": false}, "BotStatus": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "BotStatusInfo": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/BotStatus"}, "startedAt": {"type": "string", "format": "date-time", "nullable": true}, "stoppedAt": {"type": "string", "format": "date-time", "nullable": true}, "uptime": {"type": "string", "format": "date-span", "nullable": true}, "lastError": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BotStatusResponse": {"required": ["status"], "type": "object", "properties": {"status": {"type": "string", "nullable": true}, "startedAt": {"type": "string", "format": "date-time", "nullable": true}, "stoppedAt": {"type": "string", "format": "date-time", "nullable": true}, "uptime": {"type": "string", "format": "date-span", "nullable": true}, "lastError": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PlaceOrderRequest": {"type": "object", "properties": {"symbol": {"type": "string", "nullable": true}, "orderType": {"type": "string", "nullable": true}, "side": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}}}}