﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArbitrageBot.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ArbitrageOpportunities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TargetSymbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    LowPriceExchange = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    LowPrice = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    HighPriceExchange = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    HighPrice = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    PotentialProfitPercentage = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: false),
                    DetectedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ArbitrageOpportunities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Balances",
                columns: table => new
                {
                    Exchange = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Asset = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Free = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    Locked = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Balances", x => new { x.Exchange, x.Asset });
                });

            migrationBuilder.CreateTable(
                name: "Orders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Exchange = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    OrderType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Side = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Price = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Trades",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Exchange = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    OrderId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Price = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Trades", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ArbitrageOpportunities_DetectedAt",
                table: "ArbitrageOpportunities",
                column: "DetectedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ArbitrageOpportunities_LowPriceExchange_HighPriceExchange",
                table: "ArbitrageOpportunities",
                columns: new[] { "LowPriceExchange", "HighPriceExchange" });

            migrationBuilder.CreateIndex(
                name: "IX_ArbitrageOpportunities_PotentialProfitPercentage",
                table: "ArbitrageOpportunities",
                column: "PotentialProfitPercentage");

            migrationBuilder.CreateIndex(
                name: "IX_ArbitrageOpportunities_TargetSymbol",
                table: "ArbitrageOpportunities",
                column: "TargetSymbol");

            migrationBuilder.CreateIndex(
                name: "IX_Balances_Asset",
                table: "Balances",
                column: "Asset");

            migrationBuilder.CreateIndex(
                name: "IX_Balances_Exchange",
                table: "Balances",
                column: "Exchange");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Exchange",
                table: "Orders",
                column: "Exchange");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Exchange_Symbol",
                table: "Orders",
                columns: new[] { "Exchange", "Symbol" });

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Status",
                table: "Orders",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Symbol",
                table: "Orders",
                column: "Symbol");

            migrationBuilder.CreateIndex(
                name: "IX_Trades_Exchange",
                table: "Trades",
                column: "Exchange");

            migrationBuilder.CreateIndex(
                name: "IX_Trades_Exchange_Symbol",
                table: "Trades",
                columns: new[] { "Exchange", "Symbol" });

            migrationBuilder.CreateIndex(
                name: "IX_Trades_Symbol",
                table: "Trades",
                column: "Symbol");

            migrationBuilder.CreateIndex(
                name: "IX_Trades_Timestamp",
                table: "Trades",
                column: "Timestamp");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ArbitrageOpportunities");

            migrationBuilder.DropTable(
                name: "Balances");

            migrationBuilder.DropTable(
                name: "Orders");

            migrationBuilder.DropTable(
                name: "Trades");
        }
    }
}
