using ArbitrageBot.Core.Models;
using ArbitrageBot.Host.Services;
using ArbitrageBot.Host.Hubs;
using ArbitrageBot.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ArbitrageBot.Host.Workers;

/// <summary>
/// Background service responsible for analyzing price data and identifying arbitrage opportunities
/// </summary>
public class ArbitrageStrategyWorker : BackgroundService
{
    private readonly IPriceDataService _priceDataService;
    private readonly IServiceProvider _serviceProvider;
    private readonly IBotControlService _botControlService;
    private readonly IArbitrageDashboardService _dashboardService;
    private readonly ILogger<ArbitrageStrategyWorker> _logger;
    
    // Configuration for arbitrage strategy
    private readonly decimal _minimumProfitPercentage = 0.5m; // 0.5% minimum profit
    private readonly decimal _tradingFeePercentage = 0.1m; // 0.1% trading fee per exchange
    private readonly decimal _minimumTradeAmount = 100m; // Minimum $100 trade
    
    // Symbols to analyze for arbitrage
    private readonly string[] _symbolsToAnalyze = { "BTC/USDT", "ETH/USDT", "BNB/USDT" };

    public ArbitrageStrategyWorker(
        IPriceDataService priceDataService,
        IServiceProvider serviceProvider,
        IBotControlService botControlService,
        IArbitrageDashboardService dashboardService,
        ILogger<ArbitrageStrategyWorker> logger)
    {
        _priceDataService = priceDataService ?? throw new ArgumentNullException(nameof(priceDataService));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _botControlService = botControlService ?? throw new ArgumentNullException(nameof(botControlService));
        _dashboardService = dashboardService ?? throw new ArgumentNullException(nameof(dashboardService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("ArbitrageStrategyWorker starting up...");
        _logger.LogInformation("Minimum profit threshold: {MinProfit}%", _minimumProfitPercentage);
        _logger.LogInformation("Trading fee per exchange: {TradingFee}%", _tradingFeePercentage);
        _logger.LogInformation("Analyzing symbols: {Symbols}", string.Join(", ", _symbolsToAnalyze));

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                // Check if bot is running before analyzing opportunities
                if (_botControlService.Status == BotStatus.Running)
                {
                    // Use combined cancellation token (both service stopping and bot control)
                    using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                        stoppingToken, _botControlService.BotCancellationToken);

                    await AnalyzeArbitrageOpportunities(combinedCts.Token);
                }
                else
                {
                    _logger.LogDebug("Bot is not running, skipping arbitrage analysis. Status: {Status}",
                        _botControlService.Status);
                }

                // Analyze every 3 seconds for quick opportunity detection
                await Task.Delay(TimeSpan.FromSeconds(3), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("ArbitrageStrategyWorker is stopping due to cancellation request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in ArbitrageStrategyWorker");
            throw;
        }
        finally
        {
            _logger.LogInformation("ArbitrageStrategyWorker has stopped");
        }
    }

    private async Task AnalyzeArbitrageOpportunities(CancellationToken stoppingToken)
    {
        foreach (var symbol in _symbolsToAnalyze)
        {
            try
            {
                var prices = _priceDataService.GetPricesForSymbol(symbol);
                
                if (prices.Count < 2)
                {
                    _logger.LogDebug("Not enough price data for {Symbol} (only {Count} exchanges)", 
                        symbol, prices.Count);
                    continue;
                }

                var opportunity = FindBestArbitrageOpportunity(symbol, prices);
                
                if (opportunity != null)
                {
                    await LogArbitrageOpportunity(opportunity, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error analyzing arbitrage for symbol {Symbol}", symbol);
            }
        }
    }

    private ArbitrageOpportunityInfo? FindBestArbitrageOpportunity(string symbol, Dictionary<string, decimal> prices)
    {
        ArbitrageOpportunityInfo? bestOpportunity = null;
        decimal bestProfitPercentage = 0;

        // Compare all exchange pairs
        foreach (var buyExchange in prices)
        {
            foreach (var sellExchange in prices)
            {
                if (buyExchange.Key == sellExchange.Key)
                    continue;

                var buyPrice = buyExchange.Value;
                var sellPrice = sellExchange.Value;
                
                // Calculate profit considering trading fees
                var totalFees = (buyPrice + sellPrice) * (_tradingFeePercentage / 100);
                var grossProfit = sellPrice - buyPrice;
                var netProfit = grossProfit - totalFees;
                var profitPercentage = (netProfit / buyPrice) * 100;

                if (profitPercentage > _minimumProfitPercentage && profitPercentage > bestProfitPercentage)
                {
                    bestProfitPercentage = profitPercentage;
                    bestOpportunity = new ArbitrageOpportunityInfo
                    {
                        Symbol = symbol,
                        BuyExchange = buyExchange.Key,
                        SellExchange = sellExchange.Key,
                        BuyPrice = buyPrice,
                        SellPrice = sellPrice,
                        ProfitPercentage = profitPercentage,
                        EstimatedProfit = netProfit * (_minimumTradeAmount / buyPrice), // Profit for minimum trade
                        Timestamp = DateTime.UtcNow
                    };
                }
            }
        }

        return bestOpportunity;
    }

    private async Task LogArbitrageOpportunity(ArbitrageOpportunityInfo opportunity, CancellationToken stoppingToken)
    {
        _logger.LogInformation(
            "🚀 ARBITRAGE OPPORTUNITY FOUND! {Symbol}: Buy on {BuyExchange} at {BuyPrice:F8}, " +
            "Sell on {SellExchange} at {SellPrice:F8}, Profit: {Profit:F2}% (${EstimatedProfit:F2})",
            opportunity.Symbol, opportunity.BuyExchange, opportunity.BuyPrice,
            opportunity.SellExchange, opportunity.SellPrice, opportunity.ProfitPercentage, opportunity.EstimatedProfit);

        try
        {
            // Create ArbitrageOpportunity record for database
            var dbOpportunity = new ArbitrageOpportunity(
                Id: Guid.NewGuid(),
                TargetSymbol: opportunity.Symbol,
                LowPriceExchange: opportunity.BuyExchange,
                LowPrice: opportunity.BuyPrice,
                HighPriceExchange: opportunity.SellExchange,
                HighPrice: opportunity.SellPrice,
                PotentialProfitPercentage: opportunity.ProfitPercentage,
                DetectedAt: opportunity.Timestamp
            );

            // Broadcast the opportunity to the dashboard in real-time
            await _dashboardService.BroadcastArbitrageOpportunity(dbOpportunity);

            // Note: Database is currently disabled, so we'll just log for now
            _logger.LogInformation("Would save opportunity to database: {Opportunity}",
                System.Text.Json.JsonSerializer.Serialize(dbOpportunity));
            
            // TODO: Uncomment when database is enabled
            // using var scope = _serviceProvider.CreateScope();
            // var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            // dbContext.ArbitrageOpportunities.Add(dbOpportunity);
            // await dbContext.SaveChangesAsync(stoppingToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save arbitrage opportunity to database");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ArbitrageStrategyWorker is stopping...");
        await base.StopAsync(cancellationToken);
    }
}

/// <summary>
/// Internal class to hold arbitrage opportunity information
/// </summary>
internal class ArbitrageOpportunityInfo
{
    public required string Symbol { get; set; }
    public required string BuyExchange { get; set; }
    public required string SellExchange { get; set; }
    public decimal BuyPrice { get; set; }
    public decimal SellPrice { get; set; }
    public decimal ProfitPercentage { get; set; }
    public decimal EstimatedProfit { get; set; }
    public DateTime Timestamp { get; set; }
}
