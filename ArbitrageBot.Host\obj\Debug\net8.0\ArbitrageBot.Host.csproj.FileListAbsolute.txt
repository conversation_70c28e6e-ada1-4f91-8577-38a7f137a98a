C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.exe
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.deps.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.runtimeconfig.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Host.pdb
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Core.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Infrastructure.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Infrastructure.pdb
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\bin\Debug\net8.0\ArbitrageBot.Core.pdb
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.AssemblyInfo.cs
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\scopedcss\bundle\ArbitrageBot.Host.styles.css
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\Arbitrag.93DC56CB.Up2Date
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\refint\ArbitrageBot.Host.dll
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.pdb
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ArbitrageBot.Host.genruntimeconfig.cache
C:\Users\<USER>\source\repos\CRYPT\ArbitrageBot.Host\obj\Debug\net8.0\ref\ArbitrageBot.Host.dll
