@page "/"
@using Microsoft.AspNetCore.SignalR.Client
@using ArbitrageBot.Host.Services
@using System.Text.Json
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject ILogger<Dashboard> Logger
@implements IAsyncDisposable

<PageTitle>Dashboard - Arbitrage Bot</PageTitle>

<div class="container-fluid">
    <!-- Bot Status Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-robot"></i>
                        Bot Status
                    </h5>
                    <div class="btn-group" role="group">
                        <button class="btn btn-success btn-sm" @onclick="StartBot" disabled="@IsStarting">
                            @if (IsStarting)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                            }
                            else
                            {
                                <i class="fas fa-play"></i>
                            }
                            Start
                        </button>
                        <button class="btn btn-danger btn-sm" @onclick="StopBot" disabled="@IsStopping">
                            @if (IsStopping)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                            }
                            else
                            {
                                <i class="fas fa-stop"></i>
                            }
                            Stop
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-circle @GetStatusClass() me-2"></i>
                                <span class="fw-bold">@BotStatus?.Status</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Started:</small><br>
                            <span>@(BotStatus?.StartedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never")</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Uptime:</small><br>
                            <span>@(BotStatus?.Uptime?.ToString(@"hh\:mm\:ss") ?? "00:00:00")</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Connection:</small><br>
                            <span class="@(IsConnected ? "text-success" : "text-danger")">
                                <i class="fas fa-circle @(IsConnected ? "text-success" : "text-danger")"></i>
                                @(IsConnected ? "Connected" : "Disconnected")
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Prices Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        Live Prices
                        <small class="text-light ms-2">
                            Last updated: @(PriceData?.LastUpdated.ToString("HH:mm:ss") ?? "Never")
                        </small>
                    </h5>
                </div>
                <div class="card-body">
                    @if (PriceData?.Symbols != null && PriceData.Symbols.Any())
                    {
                        <div class="row">
                            @foreach (var symbol in PriceData.Symbols)
                            {
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card price-card h-100">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">@symbol.Key</h6>
                                        </div>
                                        <div class="card-body">
                                            @foreach (var exchange in symbol.Value)
                                            {
                                                var exchangeData = JsonSerializer.Deserialize<JsonElement>(exchange.Value.ToString());
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="fw-bold">@exchange.Key:</span>
                                                    <div class="text-end">
                                                        <div class="fw-bold text-success">
                                                            @exchangeData.GetProperty("FormattedPrice").GetString()
                                                        </div>
                                                        <small class="text-muted">
                                                            @exchangeData.GetProperty("FormattedLastUpdate").GetString()
                                                        </small>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>No price data available. Start the bot to begin collecting data.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Arbitrage Opportunities Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-coins"></i>
                        Recent Arbitrage Opportunities
                        <span class="badge bg-success ms-2">@ArbitrageOpportunities.Count</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if (ArbitrageOpportunities.Any())
                    {
                        <div class="row">
                            @foreach (var opportunity in ArbitrageOpportunities.Take(6))
                            {
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card opportunity-card h-100 @(opportunity.IsNew ? "new-opportunity" : "")">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">@opportunity.Symbol</h6>
                                                <small class="text-muted">@opportunity.FormattedTime</small>
                                            </div>
                                            <div class="mb-2">
                                                <div class="d-flex justify-content-between">
                                                    <span class="text-success">Buy: @opportunity.BuyExchange</span>
                                                    <span class="fw-bold">@opportunity.FormattedBuyPrice</span>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <span class="text-danger">Sell: @opportunity.SellExchange</span>
                                                    <span class="fw-bold">@opportunity.FormattedSellPrice</span>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <span class="badge bg-success fs-6">
                                                    <i class="fas fa-arrow-up"></i>
                                                    @opportunity.FormattedProfit
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-coins fa-3x mb-3"></i>
                            <p>No arbitrage opportunities detected yet. Start the bot to begin monitoring.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private HubConnection? hubConnection;
    private bool IsConnected => hubConnection?.State == HubConnectionState.Connected;
    private bool IsStarting = false;
    private bool IsStopping = false;

    // Data models
    private BotStatusInfo? BotStatus;
    private PriceDataModel? PriceData;
    private List<ArbitrageOpportunityModel> ArbitrageOpportunities = new();

    protected override async Task OnInitializedAsync()
    {
        // Initialize SignalR connection
        hubConnection = new HubConnectionBuilder()
            .WithUrl(Navigation.ToAbsoluteUri("/arbitrageDashboardHub"))
            .Build();

        // Set up event handlers
        hubConnection.On<object>("BotStatusUpdate", OnBotStatusUpdate);
        hubConnection.On<object>("PriceDataUpdate", OnPriceDataUpdate);
        hubConnection.On<object>("NewArbitrageOpportunity", OnNewArbitrageOpportunity);

        // Handle connection events
        hubConnection.Closed += OnConnectionClosed;
        hubConnection.Reconnected += OnReconnected;
        hubConnection.Reconnecting += OnReconnecting;

        try
        {
            await hubConnection.StartAsync();
            Logger.LogInformation("SignalR connection established");
            
            // Request initial data
            await hubConnection.SendAsync("RequestBotStatus");
            await hubConnection.SendAsync("RequestPriceData");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error establishing SignalR connection");
        }
    }

    private async Task OnBotStatusUpdate(object statusData)
    {
        try
        {
            var json = JsonSerializer.Serialize(statusData);
            BotStatus = JsonSerializer.Deserialize<BotStatusInfo>(json);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing bot status update");
        }
    }

    private async Task OnPriceDataUpdate(object priceData)
    {
        try
        {
            var json = JsonSerializer.Serialize(priceData);
            PriceData = JsonSerializer.Deserialize<PriceDataModel>(json);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing price data update");
        }
    }

    private async Task OnNewArbitrageOpportunity(object opportunityData)
    {
        try
        {
            var json = JsonSerializer.Serialize(opportunityData);
            var opportunity = JsonSerializer.Deserialize<ArbitrageOpportunityModel>(json);
            
            if (opportunity != null)
            {
                opportunity.IsNew = true;
                ArbitrageOpportunities.Insert(0, opportunity);
                
                // Keep only the last 20 opportunities
                if (ArbitrageOpportunities.Count > 20)
                {
                    ArbitrageOpportunities = ArbitrageOpportunities.Take(20).ToList();
                }
                
                await InvokeAsync(StateHasChanged);
                
                // Remove the "new" flag after a few seconds
                _ = Task.Delay(3000).ContinueWith(async _ =>
                {
                    opportunity.IsNew = false;
                    await InvokeAsync(StateHasChanged);
                });
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing new arbitrage opportunity");
        }
    }

    private async Task StartBot()
    {
        IsStarting = true;
        StateHasChanged();
        
        try
        {
            using var httpClient = new HttpClient();
            var response = await httpClient.PostAsync($"{Navigation.BaseUri}api/bot/start", null);
            
            if (response.IsSuccessStatusCode)
            {
                Logger.LogInformation("Bot start command sent successfully");
            }
            else
            {
                Logger.LogWarning("Failed to start bot: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting bot");
        }
        finally
        {
            IsStarting = false;
            StateHasChanged();
        }
    }

    private async Task StopBot()
    {
        IsStopping = true;
        StateHasChanged();
        
        try
        {
            using var httpClient = new HttpClient();
            var response = await httpClient.PostAsync($"{Navigation.BaseUri}api/bot/stop", null);
            
            if (response.IsSuccessStatusCode)
            {
                Logger.LogInformation("Bot stop command sent successfully");
            }
            else
            {
                Logger.LogWarning("Failed to stop bot: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error stopping bot");
        }
        finally
        {
            IsStopping = false;
            StateHasChanged();
        }
    }

    private string GetStatusClass()
    {
        return BotStatus?.Status switch
        {
            "Running" => "status-running pulse",
            "Stopped" => "status-stopped",
            "Starting" => "status-starting pulse",
            "Stopping" => "status-stopping pulse",
            _ => "text-muted"
        };
    }

    private async Task OnConnectionClosed(Exception? exception)
    {
        Logger.LogWarning("SignalR connection closed");
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnReconnected(string? connectionId)
    {
        Logger.LogInformation("SignalR reconnected");
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnReconnecting(Exception? exception)
    {
        Logger.LogInformation("SignalR reconnecting...");
        await InvokeAsync(StateHasChanged);
    }

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    // Data models for the dashboard
    public class BotStatusInfo
    {
        public string Status { get; set; } = "";
        public DateTime? StartedAt { get; set; }
        public DateTime? StoppedAt { get; set; }
        public TimeSpan? Uptime { get; set; }
    }

    public class PriceDataModel
    {
        public Dictionary<string, Dictionary<string, object>> Symbols { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public int TotalSymbols { get; set; }
        public int TotalExchanges { get; set; }
    }

    public class ArbitrageOpportunityModel
    {
        public Guid Id { get; set; }
        public string Symbol { get; set; } = "";
        public string BuyExchange { get; set; } = "";
        public string SellExchange { get; set; } = "";
        public decimal BuyPrice { get; set; }
        public decimal SellPrice { get; set; }
        public decimal ProfitAmount { get; set; }
        public decimal ProfitPercentage { get; set; }
        public DateTime DetectedAt { get; set; }
        public string FormattedBuyPrice { get; set; } = "";
        public string FormattedSellPrice { get; set; } = "";
        public string FormattedProfit { get; set; } = "";
        public string FormattedTime { get; set; } = "";
        public bool IsNew { get; set; }
    }
}
