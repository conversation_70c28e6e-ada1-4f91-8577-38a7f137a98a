namespace ArbitrageBot.Core.Models;

/// <summary>
/// Represents a completed trade on an exchange
/// </summary>
/// <param name="Id">Unique identifier for the trade</param>
/// <param name="Exchange">Name of the exchange where the trade occurred</param>
/// <param name="Symbol">Trading pair symbol (e.g., "BTC/USDT")</param>
/// <param name="OrderId">Exchange-specific order identifier</param>
/// <param name="Price">Price at which the trade was executed</param>
/// <param name="Amount">Amount of the base asset traded</param>
/// <param name="Timestamp">When the trade was executed</param>
public record Trade(
    Guid Id,
    string Exchange,
    string Symbol,
    string OrderId,
    decimal Price,
    decimal Amount,
    DateTime Timestamp
);
