namespace ArbitrageBot.Host.Services;

/// <summary>
/// Service for controlling the arbitrage bot's worker services
/// </summary>
public interface IBotControlService
{
    /// <summary>
    /// Gets the current status of the bot
    /// </summary>
    BotStatus Status { get; }
    
    /// <summary>
    /// Gets the cancellation token for the current bot session
    /// </summary>
    CancellationToken BotCancellationToken { get; }
    
    /// <summary>
    /// Starts the bot operations
    /// </summary>
    /// <returns>True if started successfully, false if already running</returns>
    Task<bool> StartBotAsync();
    
    /// <summary>
    /// Stops the bot operations
    /// </summary>
    /// <returns>True if stopped successfully, false if already stopped</returns>
    Task<bool> StopBotAsync();
    
    /// <summary>
    /// Gets detailed status information about the bot
    /// </summary>
    BotStatusInfo GetStatusInfo();
    
    /// <summary>
    /// Event raised when bot status changes
    /// </summary>
    event EventHandler<BotStatusChangedEventArgs>? StatusChanged;
}

/// <summary>
/// Bot status enumeration
/// </summary>
public enum BotStatus
{
    Stopped,
    Starting,
    Running,
    Stopping
}

/// <summary>
/// Detailed bot status information
/// </summary>
public record BotStatusInfo(
    BotStatus Status,
    DateTime? StartedAt,
    DateTime? StoppedAt,
    TimeSpan? Uptime,
    string? LastError
);

/// <summary>
/// Event arguments for bot status changes
/// </summary>
public class BotStatusChangedEventArgs : EventArgs
{
    public BotStatus PreviousStatus { get; }
    public BotStatus NewStatus { get; }
    public DateTime ChangedAt { get; }
    
    public BotStatusChangedEventArgs(BotStatus previousStatus, BotStatus newStatus)
    {
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        ChangedAt = DateTime.UtcNow;
    }
}
