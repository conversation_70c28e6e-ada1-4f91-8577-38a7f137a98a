{"Version": 1, "Hash": "L7+EJlUnQ7DxuG8lUTSW6S/gtcpkd3AtbjuBddqfmd4=", "Source": "ArbitrageBot.Host", "BasePath": "_content/ArbitrageBot.Host", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ArbitrageBot.Host\\wwwroot", "Source": "ArbitrageBot.Host", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\wwwroot\\", "BasePath": "_content/ArbitrageBot.Host", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\wwwroot\\css\\site.css", "SourceId": "ArbitrageBot.Host", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\wwwroot\\", "BasePath": "_content/ArbitrageBot.Host", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "oeiwix7p75", "Integrity": "MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 3071, "LastWriteTime": "2025-06-30T12:13:49+00:00"}], "Endpoints": [{"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 12:13:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k="}]}, {"Route": "css/site.oeiwix7p75.css", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 12:13:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oeiwix7p75"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k="}]}]}