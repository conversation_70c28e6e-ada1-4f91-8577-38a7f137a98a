[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_9", "RelativePath": "api/binance/connectivity", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "TestBinanceConnectivity"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_10", "RelativePath": "api/binance/price/{symbol}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "symbol", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "GetBinancePrice"}, {"ContainingType": "ArbitrageBot.Host.Controllers.BotController", "Method": "StartBot", "RelativePath": "api/Bot/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "ArbitrageBot.Host.Controllers.BotController", "Method": "GetStatus", "RelativePath": "api/Bot/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ArbitrageBot.Host.Controllers.BotStatusResponse", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ArbitrageBot.Host.Controllers.BotController", "Method": "StopBot", "RelativePath": "api/Bot/stop", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "ArbitrageBot.Host.Controllers.BotOperationResult", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_5", "RelativePath": "api/exchange/balances", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "GetBalances"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "api/exchange/connectivity", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "TestConnectivity"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_7", "RelativePath": "api/exchange/order", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "PlaceOrderRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "PlaceOrder"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_6", "RelativePath": "api/exchange/price/{symbol}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "symbol", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "GetPrice"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_11", "RelativePath": "api/prices", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "GetAllPrices"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_12", "RelativePath": "api/prices/{symbol}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "symbol", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "GetPricesForSymbol"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_4", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "EndpointName": "HealthCheck"}]