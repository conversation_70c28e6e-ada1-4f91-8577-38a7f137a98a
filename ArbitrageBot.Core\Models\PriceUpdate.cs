namespace ArbitrageBot.Core.Models;

/// <summary>
/// Represents a real-time price update from an exchange
/// </summary>
/// <param name="Exchange">Name of the exchange</param>
/// <param name="Symbol">Trading pair symbol</param>
/// <param name="Price">Current price</param>
/// <param name="Timestamp">When the price update occurred</param>
public record PriceUpdate(
    string Exchange,
    string Symbol,
    decimal Price,
    DateTime Timestamp
);
