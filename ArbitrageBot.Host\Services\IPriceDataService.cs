namespace ArbitrageBot.Host.Services;

/// <summary>
/// Service for managing real-time price data across multiple exchanges
/// </summary>
public interface IPriceDataService
{
    /// <summary>
    /// Updates the price for a specific symbol on a specific exchange
    /// </summary>
    /// <param name="exchange">Exchange name (e.g., "Binance", "Coinbase")</param>
    /// <param name="symbol">Trading symbol (e.g., "BTC/USDT")</param>
    /// <param name="price">Current price</param>
    void UpdatePrice(string exchange, string symbol, decimal price);

    /// <summary>
    /// Gets the current price for a symbol on a specific exchange
    /// </summary>
    /// <param name="exchange">Exchange name</param>
    /// <param name="symbol">Trading symbol</param>
    /// <returns>Current price or null if not available</returns>
    decimal? GetPrice(string exchange, string symbol);

    /// <summary>
    /// Gets all current prices for a specific symbol across all exchanges
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <returns>Dictionary of exchange name to price</returns>
    Dictionary<string, decimal> GetPricesForSymbol(string symbol);

    /// <summary>
    /// Gets all available symbols across all exchanges
    /// </summary>
    /// <returns>List of unique symbols</returns>
    List<string> GetAvailableSymbols();

    /// <summary>
    /// Gets all exchanges that have price data
    /// </summary>
    /// <returns>List of exchange names</returns>
    List<string> GetAvailableExchanges();

    /// <summary>
    /// Gets the timestamp when a price was last updated
    /// </summary>
    /// <param name="exchange">Exchange name</param>
    /// <param name="symbol">Trading symbol</param>
    /// <returns>Last update timestamp or null if not available</returns>
    DateTime? GetLastUpdateTime(string exchange, string symbol);
}
