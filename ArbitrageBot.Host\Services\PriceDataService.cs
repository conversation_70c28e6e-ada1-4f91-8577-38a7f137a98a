using System.Collections.Concurrent;

namespace ArbitrageBot.Host.Services;

/// <summary>
/// Thread-safe in-memory service for managing real-time price data across multiple exchanges
/// </summary>
public class PriceDataService : IPriceDataService
{
    // Key format: "exchange:symbol" -> price
    private readonly ConcurrentDictionary<string, decimal> _prices = new();
    
    // Key format: "exchange:symbol" -> timestamp
    private readonly ConcurrentDictionary<string, DateTime> _lastUpdated = new();
    
    private readonly ILogger<PriceDataService> _logger;

    public PriceDataService(ILogger<PriceDataService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public void UpdatePrice(string exchange, string symbol, decimal price)
    {
        if (string.IsNullOrWhiteSpace(exchange))
            throw new ArgumentException("Exchange cannot be null or empty", nameof(exchange));
        
        if (string.IsNullOrWhiteSpace(symbol))
            throw new ArgumentException("Symbol cannot be null or empty", nameof(symbol));
        
        if (price <= 0)
            throw new ArgumentException("Price must be greater than zero", nameof(price));

        var key = CreateKey(exchange, symbol);
        var timestamp = DateTime.UtcNow;
        
        _prices.AddOrUpdate(key, price, (k, oldPrice) => price);
        _lastUpdated.AddOrUpdate(key, timestamp, (k, oldTime) => timestamp);
        
        _logger.LogDebug("Updated price for {Exchange}:{Symbol} = {Price:F8} at {Timestamp}", 
            exchange, symbol, price, timestamp);
    }

    public decimal? GetPrice(string exchange, string symbol)
    {
        if (string.IsNullOrWhiteSpace(exchange) || string.IsNullOrWhiteSpace(symbol))
            return null;

        var key = CreateKey(exchange, symbol);
        return _prices.TryGetValue(key, out var price) ? price : null;
    }

    public Dictionary<string, decimal> GetPricesForSymbol(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return new Dictionary<string, decimal>();

        var result = new Dictionary<string, decimal>();
        
        foreach (var kvp in _prices)
        {
            var parts = kvp.Key.Split(':', 2);
            if (parts.Length == 2 && parts[1].Equals(symbol, StringComparison.OrdinalIgnoreCase))
            {
                result[parts[0]] = kvp.Value;
            }
        }
        
        return result;
    }

    public List<string> GetAvailableSymbols()
    {
        var symbols = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        
        foreach (var key in _prices.Keys)
        {
            var parts = key.Split(':', 2);
            if (parts.Length == 2)
            {
                symbols.Add(parts[1]);
            }
        }
        
        return symbols.ToList();
    }

    public List<string> GetAvailableExchanges()
    {
        var exchanges = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        
        foreach (var key in _prices.Keys)
        {
            var parts = key.Split(':', 2);
            if (parts.Length == 2)
            {
                exchanges.Add(parts[0]);
            }
        }
        
        return exchanges.ToList();
    }

    public DateTime? GetLastUpdateTime(string exchange, string symbol)
    {
        if (string.IsNullOrWhiteSpace(exchange) || string.IsNullOrWhiteSpace(symbol))
            return null;

        var key = CreateKey(exchange, symbol);
        return _lastUpdated.TryGetValue(key, out var timestamp) ? timestamp : null;
    }

    private static string CreateKey(string exchange, string symbol)
    {
        return $"{exchange}:{symbol}";
    }
}
