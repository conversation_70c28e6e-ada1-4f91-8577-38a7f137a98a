using Microsoft.AspNetCore.SignalR;
using ArbitrageBot.Core.Models;
using ArbitrageBot.Host.Services;

namespace ArbitrageBot.Host.Hubs;

/// <summary>
/// SignalR hub for real-time arbitrage dashboard updates
/// </summary>
public class ArbitrageDashboardHub : Hub
{
    private readonly ILogger<ArbitrageDashboardHub> _logger;
    private readonly IPriceDataService _priceDataService;
    private readonly IBotControlService _botControlService;

    public ArbitrageDashboardHub(
        ILogger<ArbitrageDashboardHub> logger,
        IPriceDataService priceDataService,
        IBotControlService botControlService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _priceDataService = priceDataService ?? throw new ArgumentNullException(nameof(priceDataService));
        _botControlService = botControlService ?? throw new ArgumentNullException(nameof(botControlService));
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected to ArbitrageDashboardHub: {ConnectionId}", Context.ConnectionId);
        
        // Send initial data to the newly connected client
        await SendInitialData();
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client disconnected from ArbitrageDashboardHub: {ConnectionId}", Context.ConnectionId);
        
        if (exception != null)
        {
            _logger.LogError(exception, "Client disconnected with error: {ConnectionId}", Context.ConnectionId);
        }
        
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Client can request current price data
    /// </summary>
    public async Task RequestPriceData()
    {
        try
        {
            var priceData = GetCurrentPriceData();
            await Clients.Caller.SendAsync("PriceDataUpdate", priceData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending price data to client {ConnectionId}", Context.ConnectionId);
        }
    }

    /// <summary>
    /// Client can request current bot status
    /// </summary>
    public async Task RequestBotStatus()
    {
        try
        {
            var statusInfo = _botControlService.GetStatusInfo();
            await Clients.Caller.SendAsync("BotStatusUpdate", statusInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bot status to client {ConnectionId}", Context.ConnectionId);
        }
    }

    /// <summary>
    /// Send initial data to newly connected client
    /// </summary>
    private async Task SendInitialData()
    {
        try
        {
            // Send current price data
            var priceData = GetCurrentPriceData();
            await Clients.Caller.SendAsync("PriceDataUpdate", priceData);

            // Send current bot status
            var statusInfo = _botControlService.GetStatusInfo();
            await Clients.Caller.SendAsync("BotStatusUpdate", statusInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending initial data to client {ConnectionId}", Context.ConnectionId);
        }
    }

    /// <summary>
    /// Get current price data in a format suitable for the dashboard
    /// </summary>
    private object GetCurrentPriceData()
    {
        var symbols = _priceDataService.GetAvailableSymbols();
        var exchanges = _priceDataService.GetAvailableExchanges();

        var priceData = new Dictionary<string, Dictionary<string, object>>();

        foreach (var symbol in symbols)
        {
            var symbolPrices = new Dictionary<string, object>();
            
            foreach (var exchange in exchanges)
            {
                var price = _priceDataService.GetPrice(exchange, symbol);
                var lastUpdate = _priceDataService.GetLastUpdateTime(exchange, symbol);

                if (price.HasValue)
                {
                    symbolPrices[exchange] = new
                    {
                        Price = price.Value,
                        LastUpdate = lastUpdate,
                        FormattedPrice = $"${price.Value:F2}",
                        FormattedLastUpdate = lastUpdate?.ToString("HH:mm:ss") ?? "Never"
                    };
                }
            }

            if (symbolPrices.Any())
            {
                priceData[symbol] = symbolPrices;
            }
        }

        return new
        {
            Symbols = priceData,
            LastUpdated = DateTime.UtcNow,
            TotalSymbols = symbols.Count,
            TotalExchanges = exchanges.Count
        };
    }
}

/// <summary>
/// Service for broadcasting updates to all connected dashboard clients
/// </summary>
public interface IArbitrageDashboardService
{
    Task BroadcastArbitrageOpportunity(ArbitrageOpportunity opportunity);
    Task BroadcastPriceUpdate();
    Task BroadcastBotStatusUpdate();
}

/// <summary>
/// Implementation of the dashboard broadcasting service
/// </summary>
public class ArbitrageDashboardService : IArbitrageDashboardService
{
    private readonly IHubContext<ArbitrageDashboardHub> _hubContext;
    private readonly IPriceDataService _priceDataService;
    private readonly IBotControlService _botControlService;
    private readonly ILogger<ArbitrageDashboardService> _logger;

    public ArbitrageDashboardService(
        IHubContext<ArbitrageDashboardHub> hubContext,
        IPriceDataService priceDataService,
        IBotControlService botControlService,
        ILogger<ArbitrageDashboardService> logger)
    {
        _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
        _priceDataService = priceDataService ?? throw new ArgumentNullException(nameof(priceDataService));
        _botControlService = botControlService ?? throw new ArgumentNullException(nameof(botControlService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task BroadcastArbitrageOpportunity(ArbitrageOpportunity opportunity)
    {
        try
        {
            var opportunityData = new
            {
                Id = Guid.NewGuid(),
                Symbol = opportunity.TargetSymbol,
                BuyExchange = opportunity.LowPriceExchange,
                SellExchange = opportunity.HighPriceExchange,
                BuyPrice = opportunity.LowPrice,
                SellPrice = opportunity.HighPrice,
                ProfitAmount = opportunity.PriceDifference,
                ProfitPercentage = opportunity.PotentialProfitPercentage,
                DetectedAt = opportunity.DetectedAt,
                FormattedBuyPrice = $"${opportunity.LowPrice:F2}",
                FormattedSellPrice = $"${opportunity.HighPrice:F2}",
                FormattedProfit = $"${opportunity.PriceDifference:F2} ({opportunity.PotentialProfitPercentage:F2}%)",
                FormattedTime = opportunity.DetectedAt.ToString("HH:mm:ss")
            };

            await _hubContext.Clients.All.SendAsync("NewArbitrageOpportunity", opportunityData);
            _logger.LogDebug("Broadcasted arbitrage opportunity for {Symbol}", opportunity.TargetSymbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting arbitrage opportunity");
        }
    }

    public async Task BroadcastPriceUpdate()
    {
        try
        {
            var priceData = GetCurrentPriceData();
            await _hubContext.Clients.All.SendAsync("PriceDataUpdate", priceData);
            _logger.LogDebug("Broadcasted price data update");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting price update");
        }
    }

    public async Task BroadcastBotStatusUpdate()
    {
        try
        {
            var statusInfo = _botControlService.GetStatusInfo();
            await _hubContext.Clients.All.SendAsync("BotStatusUpdate", statusInfo);
            _logger.LogDebug("Broadcasted bot status update");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting bot status update");
        }
    }

    private object GetCurrentPriceData()
    {
        var symbols = _priceDataService.GetAvailableSymbols();
        var exchanges = _priceDataService.GetAvailableExchanges();

        var priceData = new Dictionary<string, Dictionary<string, object>>();

        foreach (var symbol in symbols)
        {
            var symbolPrices = new Dictionary<string, object>();
            
            foreach (var exchange in exchanges)
            {
                var price = _priceDataService.GetPrice(exchange, symbol);
                var lastUpdate = _priceDataService.GetLastUpdateTime(exchange, symbol);

                if (price.HasValue)
                {
                    symbolPrices[exchange] = new
                    {
                        Price = price.Value,
                        LastUpdate = lastUpdate,
                        FormattedPrice = $"${price.Value:F2}",
                        FormattedLastUpdate = lastUpdate?.ToString("HH:mm:ss") ?? "Never"
                    };
                }
            }

            if (symbolPrices.Any())
            {
                priceData[symbol] = symbolPrices;
            }
        }

        return new
        {
            Symbols = priceData,
            LastUpdated = DateTime.UtcNow,
            TotalSymbols = symbols.Count,
            TotalExchanges = exchanges.Count
        };
    }
}
