using ArbitrageBot.Host.Services;
using Microsoft.AspNetCore.Mvc;

namespace ArbitrageBot.Host.Controllers;

/// <summary>
/// API Controller for managing the arbitrage bot operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class BotController : ControllerBase
{
    private readonly IBotControlService _botControlService;
    private readonly ILogger<BotController> _logger;

    public BotController(IBotControlService botControlService, ILogger<BotController> logger)
    {
        _botControlService = botControlService ?? throw new ArgumentNullException(nameof(botControlService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Starts the arbitrage bot
    /// </summary>
    /// <returns>Result of the start operation</returns>
    [HttpPost("start")]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<BotOperationResult>> StartBot()
    {
        try
        {
            _logger.LogInformation("Received request to start arbitrage bot");
            
            var success = await _botControlService.StartBotAsync();
            var statusInfo = _botControlService.GetStatusInfo();
            
            if (success)
            {
                var result = new BotOperationResult
                {
                    Success = true,
                    Message = "Arbitrage bot started successfully",
                    Status = statusInfo.Status.ToString(),
                    Timestamp = DateTime.UtcNow,
                    StatusInfo = statusInfo
                };
                
                _logger.LogInformation("Arbitrage bot start request completed successfully");
                return Ok(result);
            }
            else
            {
                var result = new BotOperationResult
                {
                    Success = false,
                    Message = "Failed to start arbitrage bot - it may already be running",
                    Status = statusInfo.Status.ToString(),
                    Timestamp = DateTime.UtcNow,
                    StatusInfo = statusInfo
                };
                
                _logger.LogWarning("Arbitrage bot start request failed - bot may already be running");
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while starting arbitrage bot");
            
            var result = new BotOperationResult
            {
                Success = false,
                Message = $"Internal error occurred while starting bot: {ex.Message}",
                Status = _botControlService.Status.ToString(),
                Timestamp = DateTime.UtcNow,
                StatusInfo = _botControlService.GetStatusInfo()
            };
            
            return StatusCode(StatusCodes.Status500InternalServerError, result);
        }
    }

    /// <summary>
    /// Stops the arbitrage bot
    /// </summary>
    /// <returns>Result of the stop operation</returns>
    [HttpPost("stop")]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(BotOperationResult), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<BotOperationResult>> StopBot()
    {
        try
        {
            _logger.LogInformation("Received request to stop arbitrage bot");
            
            var success = await _botControlService.StopBotAsync();
            var statusInfo = _botControlService.GetStatusInfo();
            
            if (success)
            {
                var result = new BotOperationResult
                {
                    Success = true,
                    Message = "Arbitrage bot stopped successfully",
                    Status = statusInfo.Status.ToString(),
                    Timestamp = DateTime.UtcNow,
                    StatusInfo = statusInfo
                };
                
                _logger.LogInformation("Arbitrage bot stop request completed successfully");
                return Ok(result);
            }
            else
            {
                var result = new BotOperationResult
                {
                    Success = false,
                    Message = "Failed to stop arbitrage bot - it may already be stopped",
                    Status = statusInfo.Status.ToString(),
                    Timestamp = DateTime.UtcNow,
                    StatusInfo = statusInfo
                };
                
                _logger.LogWarning("Arbitrage bot stop request failed - bot may already be stopped");
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping arbitrage bot");
            
            var result = new BotOperationResult
            {
                Success = false,
                Message = $"Internal error occurred while stopping bot: {ex.Message}",
                Status = _botControlService.Status.ToString(),
                Timestamp = DateTime.UtcNow,
                StatusInfo = _botControlService.GetStatusInfo()
            };
            
            return StatusCode(StatusCodes.Status500InternalServerError, result);
        }
    }

    /// <summary>
    /// Gets the current status of the arbitrage bot
    /// </summary>
    /// <returns>Current bot status information</returns>
    [HttpGet("status")]
    [ProducesResponseType(typeof(BotStatusResponse), StatusCodes.Status200OK)]
    public ActionResult<BotStatusResponse> GetStatus()
    {
        try
        {
            var statusInfo = _botControlService.GetStatusInfo();
            
            var response = new BotStatusResponse
            {
                Status = statusInfo.Status.ToString(),
                StartedAt = statusInfo.StartedAt,
                StoppedAt = statusInfo.StoppedAt,
                Uptime = statusInfo.Uptime,
                LastError = statusInfo.LastError,
                Timestamp = DateTime.UtcNow
            };
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting bot status");
            
            var response = new BotStatusResponse
            {
                Status = "Error",
                LastError = ex.Message,
                Timestamp = DateTime.UtcNow
            };
            
            return Ok(response);
        }
    }
}

/// <summary>
/// Response model for bot operations
/// </summary>
public class BotOperationResult
{
    public bool Success { get; set; }
    public required string Message { get; set; }
    public required string Status { get; set; }
    public DateTime Timestamp { get; set; }
    public BotStatusInfo? StatusInfo { get; set; }
}

/// <summary>
/// Response model for bot status queries
/// </summary>
public class BotStatusResponse
{
    public required string Status { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? StoppedAt { get; set; }
    public TimeSpan? Uptime { get; set; }
    public string? LastError { get; set; }
    public DateTime Timestamp { get; set; }
}
