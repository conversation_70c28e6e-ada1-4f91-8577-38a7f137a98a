{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 12:13:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k="}]}, {"Route": "css/site.oeiwix7p75.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3071"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k=\""}, {"Name": "Last-Modified", "Value": "Mon, 30 Jun 2025 12:13:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oeiwix7p75"}, {"Name": "integrity", "Value": "sha256-MEt82OQH+nYLnDy9Qg+fsEJv/i8Zib4PLfSPnqQzO1k="}, {"Name": "label", "Value": "css/site.css"}]}]}