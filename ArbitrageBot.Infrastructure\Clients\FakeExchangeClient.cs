using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Core.Models;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace ArbitrageBot.Infrastructure.Clients;

/// <summary>
/// Fake exchange client for testing and development purposes
/// Simulates exchange operations without real API calls or money
/// </summary>
public class FakeExchangeClient : IExchangeClient, IDisposable
{
    private readonly ILogger<FakeExchangeClient> _logger;
    private readonly string _exchangeName;
    private readonly Random _random;
    private readonly Timer _priceUpdateTimer;
    
    // In-memory storage
    private readonly ConcurrentDictionary<string, decimal> _balances;
    private readonly ConcurrentDictionary<string, Order> _orders;
    private readonly ConcurrentDictionary<string, decimal> _currentPrices;
    private readonly ConcurrentDictionary<string, decimal> _basePrices;
    
    // Price update subscriptions
    private readonly ConcurrentDictionary<string, List<Func<string, decimal, DateTime, Task>>> _priceSubscriptions;
    
    private bool _disposed;

    public FakeExchangeClient(string exchangeName, ILogger<FakeExchangeClient> logger)
    {
        _exchangeName = exchangeName ?? throw new ArgumentNullException(nameof(exchangeName));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _random = new Random();
        
        _balances = new ConcurrentDictionary<string, decimal>();
        _orders = new ConcurrentDictionary<string, Order>();
        _currentPrices = new ConcurrentDictionary<string, decimal>();
        _basePrices = new ConcurrentDictionary<string, decimal>();
        _priceSubscriptions = new ConcurrentDictionary<string, List<Func<string, decimal, DateTime, Task>>>();
        
        // Initialize with some default balances for testing
        InitializeDefaultBalances();
        
        // Initialize with some default prices
        InitializeDefaultPrices();
        
        // Start price fluctuation timer (updates every 5 seconds)
        _priceUpdateTimer = new Timer(UpdatePrices, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
        
        _logger.LogInformation("FakeExchangeClient initialized for exchange: {ExchangeName}", _exchangeName);
    }

    public string ExchangeName => _exchangeName;

    public Task<Order> PlaceOrderAsync(string symbol, string orderType, string side, decimal amount, decimal? price = null, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        var orderId = Guid.NewGuid();
        var orderPrice = price ?? GetCurrentPrice(symbol);
        
        var order = new Order(
            Id: orderId,
            Exchange: _exchangeName,
            Symbol: symbol,
            OrderType: orderType,
            Side: side,
            Price: orderPrice,
            Amount: amount,
            Status: "NEW"
        );
        
        _orders.TryAdd(orderId.ToString(), order);
        
        _logger.LogInformation("Order placed: {OrderId} - {Side} {Amount} {Symbol} at {Price} ({OrderType})", 
            orderId, side, amount, symbol, orderPrice, orderType);
        
        // Simulate immediate execution for market orders
        if (orderType.Equals("Market", StringComparison.OrdinalIgnoreCase))
        {
            _ = Task.Run(async () => await SimulateOrderExecution(orderId.ToString()), cancellationToken);
        }
        
        return Task.FromResult(order);
    }

    public Task<Order> CancelOrderAsync(string orderId, string symbol, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        if (_orders.TryGetValue(orderId, out var existingOrder))
        {
            var cancelledOrder = existingOrder with { Status = "CANCELLED" };
            _orders.TryUpdate(orderId, cancelledOrder, existingOrder);
            
            _logger.LogInformation("Order cancelled: {OrderId}", orderId);
            return Task.FromResult(cancelledOrder);
        }
        
        throw new InvalidOperationException($"Order {orderId} not found");
    }

    public Task<Order> GetOrderStatusAsync(string orderId, string symbol, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        if (_orders.TryGetValue(orderId, out var order))
        {
            return Task.FromResult(order);
        }
        
        throw new InvalidOperationException($"Order {orderId} not found");
    }

    public Task<IEnumerable<Balance>> GetBalancesAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        var balances = _balances.Select(kvp => new Balance(
            Exchange: _exchangeName,
            Asset: kvp.Key,
            Free: kvp.Value,
            Locked: 0m // Simplified - no locked balances in this fake implementation
        )).ToList();
        
        return Task.FromResult<IEnumerable<Balance>>(balances);
    }

    public Task<Balance?> GetBalanceAsync(string asset, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        if (_balances.TryGetValue(asset, out var amount))
        {
            var balance = new Balance(
                Exchange: _exchangeName,
                Asset: asset,
                Free: amount,
                Locked: 0m
            );
            return Task.FromResult<Balance?>(balance);
        }
        
        return Task.FromResult<Balance?>(null);
    }

    public Task<decimal> GetTickerPriceAsync(string symbol, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        var price = GetCurrentPrice(symbol);
        return Task.FromResult(price);
    }

    public async Task SubscribeToPriceUpdatesAsync(IEnumerable<string> symbols, Func<string, decimal, DateTime, Task> onPriceUpdate, CancellationToken cancellationToken = default)
    {
        foreach (var symbol in symbols)
        {
            _priceSubscriptions.AddOrUpdate(symbol,
                new List<Func<string, decimal, DateTime, Task>> { onPriceUpdate },
                (key, existing) =>
                {
                    existing.Add(onPriceUpdate);
                    return existing;
                });

            _logger.LogInformation("Subscribed to price updates for {Symbol} on {Exchange}", symbol, _exchangeName);
        }

        // Keep the subscription alive until cancellation
        try
        {
            await Task.Delay(Timeout.Infinite, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            // Clean up subscriptions
            foreach (var symbol in symbols)
            {
                if (_priceSubscriptions.TryGetValue(symbol, out var callbacks))
                {
                    callbacks.Remove(onPriceUpdate);
                    if (callbacks.Count == 0)
                    {
                        _priceSubscriptions.TryRemove(symbol, out _);
                    }
                }
            }
            _logger.LogInformation("Unsubscribed from price updates for symbols: {Symbols}", string.Join(", ", symbols));
        }
    }

    public Task<bool> TestConnectivityAsync(CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();

        _logger.LogInformation("Testing connectivity to {Exchange} - Always successful for fake client", _exchangeName);
        return Task.FromResult(true);
    }

    // Helper methods
    private void InitializeDefaultBalances()
    {
        // Initialize with some test balances
        _balances.TryAdd("BTC", 1.5m);
        _balances.TryAdd("ETH", 10.0m);
        _balances.TryAdd("USDT", 50000.0m);
        _balances.TryAdd("BNB", 100.0m);
        _balances.TryAdd("ADA", 5000.0m);

        _logger.LogInformation("Initialized default balances for {Exchange}", _exchangeName);
    }

    private void InitializeDefaultPrices()
    {
        // Initialize with some realistic base prices
        var basePrices = new Dictionary<string, decimal>
        {
            { "BTC/USDT", 45000.0m },
            { "ETH/USDT", 3000.0m },
            { "BNB/USDT", 300.0m },
            { "ADA/USDT", 0.5m },
            { "ETH/BTC", 0.067m },
            { "BNB/BTC", 0.0067m }
        };

        foreach (var kvp in basePrices)
        {
            _basePrices.TryAdd(kvp.Key, kvp.Value);
            _currentPrices.TryAdd(kvp.Key, kvp.Value);
        }

        _logger.LogInformation("Initialized default prices for {Exchange}", _exchangeName);
    }

    private decimal GetCurrentPrice(string symbol)
    {
        if (_currentPrices.TryGetValue(symbol, out var price))
        {
            return price;
        }

        // If symbol not found, generate a random price based on common patterns
        var randomPrice = GenerateRandomPrice(symbol);
        _currentPrices.TryAdd(symbol, randomPrice);
        _basePrices.TryAdd(symbol, randomPrice);

        return randomPrice;
    }

    private decimal GenerateRandomPrice(string symbol)
    {
        // Generate realistic prices based on symbol patterns
        return symbol.ToUpper() switch
        {
            var s when s.Contains("BTC") => _random.Next(40000, 50000),
            var s when s.Contains("ETH") => _random.Next(2500, 3500),
            var s when s.Contains("BNB") => _random.Next(250, 350),
            var s when s.Contains("ADA") => (decimal)(_random.NextDouble() * 0.8 + 0.3),
            _ => (decimal)(_random.NextDouble() * 100 + 1)
        };
    }

    private async void UpdatePrices(object? state)
    {
        if (_disposed) return;

        try
        {
            var updatedSymbols = new List<string>();

            // Update prices with random fluctuations
            foreach (var kvp in _basePrices.ToList())
            {
                var symbol = kvp.Key;
                var basePrice = kvp.Value;

                // Generate price fluctuation between -2% to +2%
                var fluctuation = (decimal)(_random.NextDouble() * 0.04 - 0.02);
                var newPrice = basePrice * (1 + fluctuation);

                // Ensure price doesn't go negative
                newPrice = Math.Max(newPrice, basePrice * 0.01m);

                _currentPrices.AddOrUpdate(symbol, newPrice, (key, oldValue) => newPrice);
                updatedSymbols.Add(symbol);
            }

            // Notify subscribers
            var now = DateTime.UtcNow;
            foreach (var symbol in updatedSymbols)
            {
                if (_priceSubscriptions.TryGetValue(symbol, out var callbacks) && callbacks.Count > 0)
                {
                    var price = _currentPrices[symbol];

                    // Call all subscribers for this symbol
                    var tasks = callbacks.Select(callback =>
                    {
                        try
                        {
                            return callback(symbol, price, now);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error in price update callback for {Symbol}", symbol);
                            return Task.CompletedTask;
                        }
                    });

                    await Task.WhenAll(tasks);
                }
            }

            if (updatedSymbols.Count > 0)
            {
                _logger.LogDebug("Updated prices for {Count} symbols on {Exchange}", updatedSymbols.Count, _exchangeName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating prices for {Exchange}", _exchangeName);
        }
    }

    private async Task SimulateOrderExecution(string orderId)
    {
        try
        {
            // Simulate processing delay
            await Task.Delay(_random.Next(100, 1000));

            if (_orders.TryGetValue(orderId, out var order))
            {
                // Update order status to filled
                var filledOrder = order with { Status = "FILLED" };
                _orders.TryUpdate(orderId, filledOrder, order);

                // Update balances (simplified simulation)
                await SimulateBalanceUpdate(order);

                _logger.LogInformation("Order executed: {OrderId} - {Side} {Amount} {Symbol} at {Price}",
                    orderId, order.Side, order.Amount, order.Symbol, order.Price);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error simulating order execution for {OrderId}", orderId);
        }
    }

    private async Task SimulateBalanceUpdate(Order order)
    {
        await Task.Delay(10); // Simulate async operation

        // Extract base and quote assets from symbol (e.g., "BTC/USDT" -> "BTC", "USDT")
        var parts = order.Symbol.Split('/');
        if (parts.Length != 2) return;

        var baseAsset = parts[0];
        var quoteAsset = parts[1];

        if (order.Side.Equals("BUY", StringComparison.OrdinalIgnoreCase))
        {
            // Buying: decrease quote asset, increase base asset
            _balances.AddOrUpdate(quoteAsset,
                key => 0,
                (key, current) => Math.Max(0, current - (order.Amount * order.Price)));

            _balances.AddOrUpdate(baseAsset,
                key => order.Amount,
                (key, current) => current + order.Amount);
        }
        else if (order.Side.Equals("SELL", StringComparison.OrdinalIgnoreCase))
        {
            // Selling: decrease base asset, increase quote asset
            _balances.AddOrUpdate(baseAsset,
                key => 0,
                (key, current) => Math.Max(0, current - order.Amount));

            _balances.AddOrUpdate(quoteAsset,
                key => order.Amount * order.Price,
                (key, current) => current + (order.Amount * order.Price));
        }

        _logger.LogDebug("Updated balances after {Side} order execution", order.Side);
    }

    public void Dispose()
    {
        if (_disposed) return;

        _priceUpdateTimer?.Dispose();
        _disposed = true;

        _logger.LogInformation("FakeExchangeClient disposed for exchange: {ExchangeName}", _exchangeName);
    }
}
