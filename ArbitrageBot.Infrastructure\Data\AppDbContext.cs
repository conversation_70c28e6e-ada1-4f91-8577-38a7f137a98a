using ArbitrageBot.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace ArbitrageBot.Infrastructure.Data;

/// <summary>
/// Application database context for the arbitrage bot
/// </summary>
public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Completed trades
    /// </summary>
    public DbSet<Trade> Trades { get; set; } = null!;

    /// <summary>
    /// Exchange orders
    /// </summary>
    public DbSet<Order> Orders { get; set; } = null!;

    /// <summary>
    /// Account balances
    /// </summary>
    public DbSet<Balance> Balances { get; set; } = null!;

    /// <summary>
    /// Detected arbitrage opportunities
    /// </summary>
    public DbSet<ArbitrageOpportunity> ArbitrageOpportunities { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Trade entity
        modelBuilder.Entity<Trade>(entity =>
        {
            entity.HasKey(t => t.Id);
            entity.Property(t => t.Exchange).IsRequired().HasMaxLength(50);
            entity.Property(t => t.Symbol).IsRequired().HasMaxLength(20);
            entity.Property(t => t.OrderId).IsRequired().HasMaxLength(100);
            entity.Property(t => t.Price).HasPrecision(18, 8);
            entity.Property(t => t.Amount).HasPrecision(18, 8);
            entity.Property(t => t.Timestamp).IsRequired();

            // Indexes for performance
            entity.HasIndex(t => t.Exchange);
            entity.HasIndex(t => t.Symbol);
            entity.HasIndex(t => t.Timestamp);
            entity.HasIndex(t => new { t.Exchange, t.Symbol });
        });

        // Configure Order entity
        modelBuilder.Entity<Order>(entity =>
        {
            entity.HasKey(o => o.Id);
            entity.Property(o => o.Exchange).IsRequired().HasMaxLength(50);
            entity.Property(o => o.Symbol).IsRequired().HasMaxLength(20);
            entity.Property(o => o.OrderType).IsRequired().HasMaxLength(20);
            entity.Property(o => o.Side).IsRequired().HasMaxLength(10);
            entity.Property(o => o.Price).HasPrecision(18, 8);
            entity.Property(o => o.Amount).HasPrecision(18, 8);
            entity.Property(o => o.Status).IsRequired().HasMaxLength(20);

            // Indexes for performance
            entity.HasIndex(o => o.Exchange);
            entity.HasIndex(o => o.Symbol);
            entity.HasIndex(o => o.Status);
            entity.HasIndex(o => new { o.Exchange, o.Symbol });
        });

        // Configure Balance entity
        modelBuilder.Entity<Balance>(entity =>
        {
            entity.HasKey(b => new { b.Exchange, b.Asset });
            entity.Property(b => b.Exchange).IsRequired().HasMaxLength(50);
            entity.Property(b => b.Asset).IsRequired().HasMaxLength(20);
            entity.Property(b => b.Free).HasPrecision(18, 8);
            entity.Property(b => b.Locked).HasPrecision(18, 8);

            // Indexes for performance
            entity.HasIndex(b => b.Exchange);
            entity.HasIndex(b => b.Asset);
        });

        // Configure ArbitrageOpportunity entity
        modelBuilder.Entity<ArbitrageOpportunity>(entity =>
        {
            entity.HasKey(a => a.Id);
            entity.Property(a => a.TargetSymbol).IsRequired().HasMaxLength(20);
            entity.Property(a => a.LowPriceExchange).IsRequired().HasMaxLength(50);
            entity.Property(a => a.LowPrice).HasPrecision(18, 8);
            entity.Property(a => a.HighPriceExchange).IsRequired().HasMaxLength(50);
            entity.Property(a => a.HighPrice).HasPrecision(18, 8);
            entity.Property(a => a.PotentialProfitPercentage).HasPrecision(5, 4);
            entity.Property(a => a.DetectedAt).IsRequired();

            // Indexes for performance
            entity.HasIndex(a => a.TargetSymbol);
            entity.HasIndex(a => a.DetectedAt);
            entity.HasIndex(a => a.PotentialProfitPercentage);
            entity.HasIndex(a => new { a.LowPriceExchange, a.HighPriceExchange });
        });
    }
}
