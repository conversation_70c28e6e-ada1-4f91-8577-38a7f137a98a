{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\ArbitrageBot.Host.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj", "projectName": "ArbitrageBot.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\ArbitrageBot.Host.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\ArbitrageBot.Host.csproj", "projectName": "ArbitrageBot.Host", "projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\ArbitrageBot.Host.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Host\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj"}, "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.17, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj", "projectName": "ArbitrageBot.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\ArbitrageBot.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\CRYPT\\ArbitrageBot.Core\\ArbitrageBot.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}