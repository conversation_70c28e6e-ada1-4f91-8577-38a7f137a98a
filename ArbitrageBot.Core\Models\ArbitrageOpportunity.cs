namespace ArbitrageBot.Core.Models;

/// <summary>
/// Represents a detected arbitrage opportunity between exchanges
/// </summary>
/// <param name="Id">Unique identifier for the opportunity</param>
/// <param name="TargetSymbol">Trading pair symbol for the arbitrage</param>
/// <param name="LowPriceExchange">Exchange with the lower price</param>
/// <param name="LowPrice">Lower price on the exchange</param>
/// <param name="HighPriceExchange">Exchange with the higher price</param>
/// <param name="HighPrice">Higher price on the exchange</param>
/// <param name="PotentialProfitPercentage">Potential profit as a percentage</param>
/// <param name="DetectedAt">When the opportunity was detected</param>
public record ArbitrageOpportunity(
    Guid Id,
    string TargetSymbol,
    string LowPriceExchange,
    decimal LowPrice,
    string HighPriceExchange,
    decimal HighPrice,
    decimal PotentialProfitPercentage,
    DateTime DetectedAt
)
{
    /// <summary>
    /// Price difference between exchanges
    /// </summary>
    public decimal PriceDifference => HighPrice - LowPrice;
    
    /// <summary>
    /// Indicates if this opportunity is still potentially profitable
    /// </summary>
    public bool IsStillValid(TimeSpan maxAge) => DateTime.UtcNow - DetectedAt <= maxAge;
};
