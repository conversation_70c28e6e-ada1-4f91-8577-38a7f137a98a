using ArbitrageBot.Core.Interfaces;
using ArbitrageBot.Host.Services;
using Microsoft.Extensions.DependencyInjection;

namespace ArbitrageBot.Host.Workers;

/// <summary>
/// Background service responsible for collecting market data from exchanges and updating the price data service
/// </summary>
public class MarketDataCollectorWorker : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IPriceDataService _priceDataService;
    private readonly ILogger<MarketDataCollectorWorker> _logger;

    // Symbols to monitor for arbitrage opportunities
    private readonly string[] _symbolsToMonitor = { "BTC/USDT", "ETH/USDT", "BNB/USDT" };

    public MarketDataCollectorWorker(
        IServiceProvider serviceProvider,
        IPriceDataService priceDataService,
        ILogger<MarketDataCollectorWorker> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _priceDataService = priceDataService ?? throw new ArgumentNullException(nameof(priceDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MarketDataCollectorWorker starting up...");
        _logger.LogInformation("Monitoring symbols: {Symbols}", string.Join(", ", _symbolsToMonitor));

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await CollectMarketDataFromAllExchanges(stoppingToken);

                // Wait for 5 seconds before the next iteration (more frequent updates for arbitrage)
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            // This is expected when the service is shutting down
            _logger.LogInformation("MarketDataCollectorWorker is stopping due to cancellation request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in MarketDataCollectorWorker");
            throw; // Re-throw to ensure the service fails and can be restarted
        }
        finally
        {
            _logger.LogInformation("MarketDataCollectorWorker has stopped");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MarketDataCollectorWorker is stopping...");
        await base.StopAsync(cancellationToken);
    }

    private async Task CollectMarketDataFromAllExchanges(CancellationToken stoppingToken)
    {
        var exchangeNames = new[] { "Binance", "Coinbase" }; // Using fake exchanges for now

        foreach (var exchangeName in exchangeNames)
        {
            try
            {
                // Get the keyed exchange client
                var exchangeClient = _serviceProvider.GetKeyedService<IExchangeClient>(exchangeName);
                if (exchangeClient == null)
                {
                    _logger.LogWarning("Exchange client not found for {ExchangeName}", exchangeName);
                    continue;
                }

                await CollectPricesFromExchange(exchangeClient, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting data from exchange {ExchangeName}", exchangeName);
            }
        }
    }

    private async Task CollectPricesFromExchange(IExchangeClient exchangeClient, CancellationToken stoppingToken)
    {
        foreach (var symbol in _symbolsToMonitor)
        {
            try
            {
                var price = await exchangeClient.GetTickerPriceAsync(symbol, stoppingToken);
                _priceDataService.UpdatePrice(exchangeClient.ExchangeName, symbol, price);

                _logger.LogDebug("Updated price: {Exchange} {Symbol} = {Price:F8}",
                    exchangeClient.ExchangeName, symbol, price);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get price for {Symbol} from {Exchange}",
                    symbol, exchangeClient.ExchangeName);
            }
        }
    }
}
