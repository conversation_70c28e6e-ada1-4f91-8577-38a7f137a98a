using ArbitrageBot.Core.Interfaces;

namespace ArbitrageBot.Host.Workers;

/// <summary>
/// Background service responsible for collecting market data from exchanges
/// </summary>
public class MarketDataCollectorWorker : BackgroundService
{
    private readonly IExchangeClient _exchangeClient;
    private readonly ILogger<MarketDataCollectorWorker> _logger;

    public MarketDataCollectorWorker(
        IExchangeClient exchangeClient,
        ILogger<MarketDataCollectorWorker> logger)
    {
        _exchangeClient = exchangeClient ?? throw new ArgumentNullException(nameof(exchangeClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MarketDataCollectorWorker starting up...");
        _logger.LogInformation("Using exchange: {ExchangeName}", _exchangeClient.ExchangeName);

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Collecting market data... [{Timestamp}]", DateTime.UtcNow);

                // Wait for 10 seconds before the next iteration
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            // This is expected when the service is shutting down
            _logger.LogInformation("MarketDataCollectorWorker is stopping due to cancellation request");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred in MarketDataCollectorWorker");
            throw; // Re-throw to ensure the service fails and can be restarted
        }
        finally
        {
            _logger.LogInformation("MarketDataCollectorWorker has stopped");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MarketDataCollectorWorker is stopping...");
        await base.StopAsync(cancellationToken);
    }
}
